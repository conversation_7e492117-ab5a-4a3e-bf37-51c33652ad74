<?php
/**
 * 功能完整性测试脚本
 * 验证interactive_url_replacer.php的所有核心功能是否正常工作
 */

echo "=== 功能完整性测试 ===\n\n";

// 测试站点路径
$test_site = '/var/www/dev_sites/vincetest';

if (!is_dir($test_site) || !file_exists($test_site . '/wp-config.php')) {
    echo "❌ 测试站点不存在或不是有效的WordPress站点\n";
    echo "测试站点路径: $test_site\n";
    exit(1);
}

// 临时修改脚本以避免执行主程序
$content = file_get_contents('interactive_url_replacer.php');
$test_content = str_replace('$replacer->run();', '// $replacer->run(); // 测试模式', $content);
file_put_contents('temp_interactive_test.php', $test_content);

try {
    // 包含修改后的脚本
    include 'temp_interactive_test.php';
    
    echo "✅ 脚本加载成功\n\n";
    
    // 创建测试实例
    $replacer = new InteractiveURLReplacer();
    
    // 手动设置测试站点（通过反射访问私有属性）
    $reflection = new ReflectionClass($replacer);
    $current_site_property = $reflection->getProperty('current_site');
    $current_site_property->setAccessible(true);
    $current_site_property->setValue($replacer, array(
        'path' => $test_site,
        'name' => basename($test_site)
    ));
    
    echo "🔍 测试1: WordPress配置解析\n";
    if ($replacer->parseWpConfig()) {
        echo "✅ wp-config.php解析成功\n";
        
        $db_config = $replacer->getDbConfig();
        echo "  - 数据库名: " . $db_config['DB_NAME'] . "\n";
        echo "  - 数据库主机: " . $db_config['DB_HOST'] . "\n";
        echo "  - 表前缀: " . $db_config['table_prefix'] . "\n";
    } else {
        echo "❌ wp-config.php解析失败\n";
    }
    
    echo "\n🔍 测试2: URL猜测功能\n";
    $old_urls = $replacer->guessOldUrls();
    
    if (!empty($old_urls)) {
        echo "✅ URL猜测成功，发现 " . count($old_urls) . " 个可能的URL\n";
        
        foreach ($old_urls as $i => $url_info) {
            if ($i >= 3) break; // 只显示前3个
            echo "  " . ($i + 1) . ". " . $url_info['url'] . " (来源: " . $url_info['source'] . ", 优先级: " . $url_info['priority'] . ")\n";
        }
        
        if (count($old_urls) > 3) {
            echo "  ... 还有 " . (count($old_urls) - 3) . " 个URL\n";
        }
    } else {
        echo "❌ URL猜测失败，未发现任何URL\n";
    }
    
    echo "\n🔍 测试3: DomainNameChanger类功能\n";
    
    // 测试DomainNameChanger类是否可以正常实例化
    $test_config = array(
        'change_from' => array('http://test.com'),
        'change_to' => array('https://test.com'),
        'host' => $db_config['DB_HOST'],
        'user' => $db_config['DB_USER'],
        'pw' => $db_config['DB_PASSWORD'],
        'db' => $db_config['DB_NAME'],
        'charset' => $db_config['DB_CHARSET'],
        'debug' => false,
    );
    
    try {
        $domain_changer = new DomainNameChanger($test_config);
        echo "✅ DomainNameChanger类实例化成功\n";
        echo "  - 数据库连接正常\n";
    } catch (Exception $e) {
        echo "❌ DomainNameChanger类测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n🔍 测试4: 序列化处理函数\n";
    
    // 测试序列化函数
    $test_data = array('url' => 'http://old.com/path', 'title' => 'Test Title');
    $serialized = maybe_serialize($test_data);
    $unserialized = maybe_unserialize($serialized);
    
    if ($unserialized['url'] === 'http://old.com/path') {
        echo "✅ 序列化/反序列化函数正常\n";
    } else {
        echo "❌ 序列化/反序列化函数异常\n";
    }
    
    // 测试递归替换函数
    $test_string = 'This is http://old.com/path and http://old.com/other';
    $replaced = digui_replace($test_string, array('http://old.com'), array('https://new.com'));
    
    if (strpos($replaced, 'https://new.com') !== false && strpos($replaced, 'http://old.com') === false) {
        echo "✅ 递归替换函数正常\n";
    } else {
        echo "❌ 递归替换函数异常\n";
    }
    
    echo "\n🔍 测试5: 文件处理功能\n";
    
    // 创建临时测试文件
    $test_dir = './test_files';
    if (!is_dir($test_dir)) {
        mkdir($test_dir, 0755, true);
    }
    
    $test_file = $test_dir . '/test.css';
    file_put_contents($test_file, 'body { background: url("http://old.com/image.jpg"); }');
    
    // 测试文件处理（通过反射调用私有方法）
    $process_file_method = $reflection->getMethod('processFile');
    $process_file_method->setAccessible(true);
    
    $stats = array('files_processed' => 0, 'files_replaced' => 0);
    $process_file_method->invoke($replacer, $test_file, 'http://old.com', 'https://new.com', array('.css'), $stats);
    
    $new_content = file_get_contents($test_file);
    if (strpos($new_content, 'https://new.com') !== false) {
        echo "✅ 文件URL替换功能正常\n";
    } else {
        echo "❌ 文件URL替换功能异常\n";
    }
    
    // 清理测试文件
    unlink($test_file);
    rmdir($test_dir);
    
    echo "\n📊 功能完整性评估:\n";
    
    $tests = array(
        'WordPress配置解析' => true,
        'URL猜测功能' => !empty($old_urls),
        'DomainNameChanger类' => class_exists('DomainNameChanger'),
        '序列化处理函数' => function_exists('digui_replace'),
        '文件处理功能' => strpos($new_content, 'https://new.com') !== false
    );
    
    $passed = 0;
    $total = count($tests);
    
    foreach ($tests as $test_name => $result) {
        if ($result) {
            echo "✅ {$test_name}: 通过\n";
            $passed++;
        } else {
            echo "❌ {$test_name}: 失败\n";
        }
    }
    
    echo "\n🏆 功能完整性得分: {$passed}/{$total}\n";
    
    if ($passed == $total) {
        echo "🎉 完美！所有核心功能都正常工作！\n";
    } elseif ($passed >= $total * 0.8) {
        echo "👍 很好！大部分功能正常，可能需要小幅调整\n";
    } else {
        echo "⚠️  需要改进，部分核心功能存在问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('temp_interactive_test.php')) {
        unlink('temp_interactive_test.php');
    }
}

echo "\n=== 测试完成 ===\n";
