<?php
/**
 * 简单的URL获取演示
 * 直接展示获取过程，不依赖主类
 */

echo "=== WordPress URL获取实时演示 ===\n\n";

$site_path = '/var/www/dev_sites/vincetest';
echo "📍 测试站点: {$site_path}\n\n";

// 1. 解析wp-config.php
echo "🔧 步骤1: 解析wp-config.php配置\n";
$wp_config_path = $site_path . '/wp-config.php';

if (!file_exists($wp_config_path)) {
    echo "   ❌ wp-config.php 文件不存在\n";
    exit(1);
}

$content = file_get_contents($wp_config_path);
$db_config = array();

// 提取数据库配置
$patterns = array(
    'DB_NAME' => "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
    'DB_USER' => "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
    'DB_PASSWORD' => "/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]*)['\"]/" ,
    'DB_HOST' => "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
    'DB_CHARSET' => "/define\s*\(\s*['\"]DB_CHARSET['\"]\s*,\s*['\"]([^'\"]+)['\"]/"
);

foreach ($patterns as $key => $pattern) {
    if (preg_match($pattern, $content, $matches)) {
        $db_config[$key] = $matches[1];
    } else {
        if ($key == 'DB_HOST') {
            $db_config[$key] = 'localhost';
        } elseif ($key == 'DB_CHARSET') {
            $db_config[$key] = 'utf8mb4';
        }
    }
}

// 提取表前缀
if (preg_match("/\\\$table_prefix\s*=\s*['\"]([^'\"]*)['\"]/" , $content, $matches)) {
    $db_config['table_prefix'] = $matches[1];
} else {
    $db_config['table_prefix'] = 'wp_';
}

echo "   ✅ 成功解析数据库配置:\n";
echo "      - 主机: {$db_config['DB_HOST']}\n";
echo "      - 数据库: {$db_config['DB_NAME']}\n";
echo "      - 用户: {$db_config['DB_USER']}\n";
echo "      - 表前缀: {$db_config['table_prefix']}\n";

// 2. 从数据库获取URL
echo "\n📊 步骤2: 从数据库获取URL\n";
$db_urls = array();

try {
    $mysqli = new mysqli(
        $db_config['DB_HOST'],
        $db_config['DB_USER'], 
        $db_config['DB_PASSWORD'],
        $db_config['DB_NAME']
    );
    
    if (!$mysqli->connect_error) {
        $mysqli->set_charset($db_config['DB_CHARSET']);
        $table_prefix = $db_config['table_prefix'];
        
        $query = "SELECT option_name, option_value FROM {$table_prefix}options WHERE option_name IN ('siteurl', 'home')";
        echo "   🔍 执行SQL: {$query}\n";
        
        $result = $mysqli->query($query);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                if (!empty($row['option_value']) && strpos($row['option_value'], 'http') === 0) {
                    $url = rtrim($row['option_value'], '/');
                    $db_urls[] = array('url' => $url, 'source' => 'database_' . $row['option_name'], 'priority' => 10);
                    echo "   ✅ 找到 {$row['option_name']}: {$url}\n";
                }
            }
        }
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: {$e->getMessage()}\n";
}

// 3. 获取服务器IP
echo "\n🌐 步骤3: 获取服务器IP\n";
$ip_urls = array();
$ip_services = array('https://ip.me/ip', 'https://ipinfo.io/ip');

foreach ($ip_services as $service) {
    echo "   🔍 尝试服务: {$service}\n";
    try {
        $context = stream_context_create(array(
            'http' => array(
                'timeout' => 3,
                'user_agent' => 'WordPress URL Replacer Demo'
            )
        ));
        
        $server_ip = @file_get_contents($service, false, $context);
        if ($server_ip && filter_var(trim($server_ip), FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $server_ip = trim($server_ip);
            echo "   ✅ 获取到IP: {$server_ip}\n";
            $ip_urls[] = array('url' => "http://{$server_ip}", 'source' => 'server_ip', 'priority' => 8);
            $ip_urls[] = array('url' => "https://{$server_ip}", 'source' => 'server_ip', 'priority' => 7);
            echo "   📝 生成URL: http://{$server_ip} 和 https://{$server_ip}\n";
            break;
        } else {
            echo "   ❌ 无效响应或IP格式错误\n";
        }
    } catch (Exception $e) {
        echo "   ❌ 请求失败: {$e->getMessage()}\n";
    }
}

// 4. 生成本地开发环境URL
echo "\n🏠 步骤4: 生成本地开发环境URL\n";
$site_name = basename($site_path);
echo "   📁 站点目录名: {$site_name}\n";

$dev_urls = array();
$dev_patterns = array(
    array('url' => "http://localhost/{$site_name}", 'priority' => 9, 'desc' => '最常见本地开发模式'),
    array('url' => "https://localhost/{$site_name}", 'priority' => 8, 'desc' => 'HTTPS本地开发模式'),
    array('url' => "http://{$site_name}.local", 'priority' => 7, 'desc' => '本地域名模式'),
    array('url' => "https://{$site_name}.local", 'priority' => 6, 'desc' => 'HTTPS本地域名'),
    array('url' => "http://{$site_name}.test", 'priority' => 6, 'desc' => '测试域名模式'),
    array('url' => "http://localhost:8080/{$site_name}", 'priority' => 5, 'desc' => '8080端口模式'),
    array('url' => "http://localhost:8000/{$site_name}", 'priority' => 5, 'desc' => '8000端口模式'),
);

foreach ($dev_patterns as $pattern) {
    $dev_urls[] = array('url' => $pattern['url'], 'source' => 'development', 'priority' => $pattern['priority']);
    echo "   📝 生成: {$pattern['url']} ({$pattern['desc']})\n";
}

// 5. 合并和排序
echo "\n🎯 步骤5: 合并URL并按优先级排序\n";
$all_urls = array_merge($db_urls, $ip_urls, $dev_urls);

// 去重
$unique_urls = array();
foreach ($all_urls as $item) {
    $url = $item['url'];
    if (!isset($unique_urls[$url])) {
        $unique_urls[$url] = $item;
    } else {
        // 保留优先级更高的
        if ($item['priority'] > $unique_urls[$url]['priority']) {
            $unique_urls[$url] = $item;
        }
    }
}

// 按优先级排序
uasort($unique_urls, function($a, $b) {
    return $b['priority'] - $a['priority'];
});

echo "   📊 去重前: " . count($all_urls) . " 个URL\n";
echo "   📊 去重后: " . count($unique_urls) . " 个URL\n";

// 6. 显示最终结果
echo "\n📋 最终结果 (按优先级排序):\n";
$final_urls = array_keys($unique_urls);

foreach ($final_urls as $index => $url) {
    $num = $index + 1;
    $item = $unique_urls[$url];
    echo "   {$num}. {$url} (来源: {$item['source']}, 优先级: {$item['priority']})\n";
}

echo "\n🎉 总结:\n";
echo "   - 总共获取到 " . count($final_urls) . " 个相关URL\n";
echo "   - 第1个URL是数据库中的真实URL (最重要)\n";
echo "   - 其他URL按使用场景的可能性排序\n";
echo "   - 所有URL都经过去重和优先级排序\n";
echo "   - 用户可以快速选择合适的旧URL\n\n";

echo "💡 这就是智能URL猜测的完整过程！\n";
echo "   每个URL都有明确的来源和用途，\n";
echo "   帮助用户快速找到需要替换的旧URL。\n";
