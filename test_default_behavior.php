<?php
/**
 * 默认行为测试脚本
 * 验证修改后的确认步骤默认行为是否正确
 */

echo "=== 默认行为测试 ===\n\n";

// 临时修改脚本以避免执行主程序
$content = file_get_contents('interactive_url_replacer.php');
$test_content = str_replace('$replacer->run();', '// $replacer->run(); // 测试模式', $content);
file_put_contents('temp_default_test.php', $test_content);

try {
    include 'temp_default_test.php';
    
    echo "✅ 脚本加载成功\n\n";
    
    // 测试1：检查提示文字是否正确更新
    echo "🔍 测试1: 检查提示文字更新\n";
    
    $backup_prompt_found = false;
    $confirm_prompt_found = false;
    
    // 检查备份提示
    if (strpos($content, '是否在操作前创建数据库备份？(Y/n):') !== false) {
        echo "✅ 备份提示文字正确：(Y/n) - Y为默认选项\n";
        $backup_prompt_found = true;
    } else {
        echo "❌ 备份提示文字未正确更新\n";
    }
    
    // 检查确认提示
    if (strpos($content, '确定要执行URL替换操作吗？此操作将修改数据库和文件！(Y/n):') !== false) {
        echo "✅ 确认提示文字正确：(Y/n) - Y为默认选项\n";
        $confirm_prompt_found = true;
    } else {
        echo "❌ 确认提示文字未正确更新\n";
    }
    
    echo "\n";
    
    // 测试2：模拟默认行为逻辑
    echo "🔍 测试2: 模拟默认行为逻辑\n";
    
    // 测试备份选项逻辑
    echo "备份选项测试:\n";
    
    $backup_test_cases = array(
        '' => array('expected' => 'backup', 'description' => '空输入（按回车）'),
        'y' => array('expected' => 'backup', 'description' => '明确输入 y'),
        'Y' => array('expected' => 'backup', 'description' => '明确输入 Y'),
        'n' => array('expected' => 'skip', 'description' => '明确输入 n'),
        'N' => array('expected' => 'skip', 'description' => '明确输入 N')
    );
    
    foreach ($backup_test_cases as $input => $test) {
        $will_backup = (strtolower($input) === 'y' || empty($input));
        $result = $will_backup ? 'backup' : 'skip';
        
        if ($result === $test['expected']) {
            echo "  ✅ {$test['description']}: " . ($will_backup ? '创建备份' : '跳过备份') . "\n";
        } else {
            echo "  ❌ {$test['description']}: 期望 {$test['expected']}, 实际 {$result}\n";
        }
    }
    
    echo "\n确认操作测试:\n";
    
    $confirm_test_cases = array(
        '' => array('expected' => 'execute', 'description' => '空输入（按回车）'),
        'y' => array('expected' => 'execute', 'description' => '明确输入 y'),
        'Y' => array('expected' => 'execute', 'description' => '明确输入 Y'),
        'n' => array('expected' => 'cancel', 'description' => '明确输入 n'),
        'N' => array('expected' => 'cancel', 'description' => '明确输入 N')
    );
    
    foreach ($confirm_test_cases as $input => $test) {
        $will_execute = (strtolower($input) === 'y' || empty($input));
        $result = $will_execute ? 'execute' : 'cancel';
        
        if ($result === $test['expected']) {
            echo "  ✅ {$test['description']}: " . ($will_execute ? '执行操作' : '取消操作') . "\n";
        } else {
            echo "  ❌ {$test['description']}: 期望 {$test['expected']}, 实际 {$result}\n";
        }
    }
    
    echo "\n";
    
    // 测试3：检查代码逻辑是否正确
    echo "🔍 测试3: 检查代码逻辑\n";
    
    // 检查备份逻辑
    $backup_logic_correct = true;
    if (strpos($content, 'if (strtolower($backup_choice) === \'y\' || empty($backup_choice))') !== false) {
        echo "✅ 备份逻辑正确：空输入时创建备份\n";
    } else {
        echo "❌ 备份逻辑错误\n";
        $backup_logic_correct = false;
    }
    
    // 检查确认逻辑
    $confirm_logic_correct = true;
    if (strpos($content, 'if (strtolower($confirm) === \'y\' || empty($confirm))') !== false) {
        echo "✅ 确认逻辑正确：空输入时执行操作\n";
    } else {
        echo "❌ 确认逻辑错误\n";
        $confirm_logic_correct = false;
    }
    
    // 检查取消逻辑
    if (strpos($content, 'elseif (strtolower($confirm) === \'n\')') !== false && 
        strpos($content, 'elseif (strtolower($confirm) === \'n\' || empty($confirm))') === false) {
        echo "✅ 取消逻辑正确：只有明确输入 n 才取消\n";
    } else {
        echo "❌ 取消逻辑错误\n";
        $confirm_logic_correct = false;
    }
    
    echo "\n";
    
    // 测试4：用户体验流程模拟
    echo "🔍 测试4: 用户体验流程模拟\n";
    
    echo "场景1 - 快速确认流程（连续按两次回车）:\n";
    echo "  1. 备份选项：按回车 → 自动创建备份 ✅\n";
    echo "  2. 操作确认：按回车 → 自动确认执行 ✅\n";
    echo "  结果：用户可以快速完成确认流程\n\n";
    
    echo "场景2 - 明确拒绝流程:\n";
    echo "  1. 备份选项：输入 n → 跳过备份\n";
    echo "  2. 操作确认：输入 n → 取消操作\n";
    echo "  结果：用户仍可明确拒绝操作\n\n";
    
    echo "场景3 - 混合选择:\n";
    echo "  1. 备份选项：按回车 → 自动创建备份\n";
    echo "  2. 操作确认：输入 n → 取消操作\n";
    echo "  结果：灵活的选择组合\n\n";
    
    // 总结
    echo "📊 测试结果总结:\n";
    
    $tests_passed = 0;
    $total_tests = 4;
    
    if ($backup_prompt_found) {
        echo "✅ 备份提示文字更新: 通过\n";
        $tests_passed++;
    } else {
        echo "❌ 备份提示文字更新: 失败\n";
    }
    
    if ($confirm_prompt_found) {
        echo "✅ 确认提示文字更新: 通过\n";
        $tests_passed++;
    } else {
        echo "❌ 确认提示文字更新: 失败\n";
    }
    
    if ($backup_logic_correct) {
        echo "✅ 备份逻辑修改: 通过\n";
        $tests_passed++;
    } else {
        echo "❌ 备份逻辑修改: 失败\n";
    }
    
    if ($confirm_logic_correct) {
        echo "✅ 确认逻辑修改: 通过\n";
        $tests_passed++;
    } else {
        echo "❌ 确认逻辑修改: 失败\n";
    }
    
    echo "\n🏆 总体得分: {$tests_passed}/{$total_tests}\n";
    
    if ($tests_passed === $total_tests) {
        echo "🎉 所有测试通过！默认行为修改成功！\n";
        echo "\n✨ 新的用户体验:\n";
        echo "  ✅ 连续按两次回车即可快速确认\n";
        echo "  ✅ 默认创建备份，提高安全性\n";
        echo "  ✅ 默认确认执行，提高效率\n";
        echo "  ✅ 保持明确拒绝的选项\n";
    } else {
        echo "⚠️  部分测试失败，需要进一步检查\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('temp_default_test.php')) {
        unlink('temp_default_test.php');
    }
}

echo "\n=== 测试完成 ===\n";
echo "\n💡 使用说明:\n";
echo "1. 备份选项：直接按回车 = 创建备份（推荐）\n";
echo "2. 操作确认：直接按回车 = 确认执行\n";
echo "3. 明确拒绝：输入 n 仍可拒绝任何操作\n";
echo "4. 快速流程：连续按两次回车完成所有确认\n";
