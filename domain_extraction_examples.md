# 增强域名提取逻辑 - 使用示例

## 🎯 问题解决

### 修复前的问题
当用户输入包含路径的新URL时，第二步域名替换会丢失路径部分：

```
用户输入：
  旧URL: https://demo66.yhct.site
  新URL: https://preview.yhct.site/zhenglingzhineng

修复前的行为：
  第一步: https://demo66.yhct.site → https://preview.yhct.site/zhenglingzhineng
  第二步: demo66.yhct.site → preview.yhct.site  ❌ 丢失了 /zhenglingzhineng
```

### 修复后的行为
现在能正确处理包含路径的URL：

```
修复后的行为：
  第一步: https://demo66.yhct.site → https://preview.yhct.site/zhenglingzhineng
  第二步: demo66.yhct.site → preview.yhct.site/zhenglingzhineng  ✅ 保留完整路径
```

## 🔧 新功能特性

### 1. 完整路径保留
```
输入: https://api.example.com/v1/users
提取: api.example.com/v1/users
```

### 2. 端口号支持
```
输入: http://localhost:8080/app
提取: localhost:8080/app
```

### 3. 复杂路径处理
```
输入: https://cdn.example.com/assets/images/logo.png
提取: cdn.example.com/assets/images/logo.png
```

### 4. 查询参数过滤
```
输入: https://example.com/search?q=test&page=1
提取: example.com/search  (自动忽略查询参数)
```

### 5. 锚点过滤
```
输入: https://example.com/page#section1
提取: example.com/page  (自动忽略锚点)
```

### 6. 根路径智能处理
```
输入: https://example.com/
提取: example.com  (忽略单独的根路径)
```

## 📋 实际使用场景

### 场景1：子目录站点迁移
```bash
php interactive_url_replacer.php

# 用户输入
旧URL: https://old-domain.com
新URL: https://new-domain.com/subsite

# 脚本执行
第一步: https://old-domain.com → https://new-domain.com/subsite
第二步: old-domain.com → new-domain.com/subsite

# 替换效果
数据库中的引用：
  old-domain.com/image.jpg → new-domain.com/subsite/image.jpg
  old-domain.com/api/data → new-domain.com/subsite/api/data
```

### 场景2：开发环境到生产环境
```bash
# 用户输入
旧URL: http://localhost:8080/myapp
新URL: https://production.com/app

# 脚本执行
第一步: http://localhost:8080/myapp → https://production.com/app
第二步: localhost:8080/myapp → production.com/app

# 替换效果
配置文件中的引用：
  var api_base = 'localhost:8080/myapp'; → var api_base = 'production.com/app';
```

### 场景3：API版本升级
```bash
# 用户输入
旧URL: https://api.example.com/v1
新URL: https://api.example.com/v2

# 脚本执行
第一步: https://api.example.com/v1 → https://api.example.com/v2
第二步: api.example.com/v1 → api.example.com/v2

# 替换效果
JavaScript代码中的引用：
  fetch('api.example.com/v1/users') → fetch('api.example.com/v2/users')
```

## 🔄 向后兼容性

### 纯域名替换（保持不变）
```bash
# 用户输入
旧URL: https://old.com
新URL: https://new.com

# 脚本执行（与之前完全相同）
第一步: https://old.com → https://new.com
第二步: old.com → new.com
```

### 协议变更（只执行第一步）
```bash
# 用户输入
旧URL: http://example.com
新URL: https://example.com

# 脚本执行
第一步: http://example.com → https://example.com
第二步: 跳过（域名相同）
```

## 📊 支持的URL格式

| URL类型 | 示例 | 提取结果 | 说明 |
|---------|------|----------|------|
| 基本域名 | `https://example.com` | `example.com` | 标准域名 |
| 带路径 | `https://example.com/path` | `example.com/path` | 保留路径 |
| 带端口 | `http://localhost:8080` | `localhost:8080` | 保留端口 |
| 端口+路径 | `http://localhost:8080/app` | `localhost:8080/app` | 保留端口和路径 |
| 子域名 | `https://api.example.com/v1` | `api.example.com/v1` | 保留子域名和路径 |
| 复杂路径 | `https://cdn.com/assets/img/logo.png` | `cdn.com/assets/img/logo.png` | 保留完整路径 |
| 带查询参数 | `https://example.com/search?q=test` | `example.com/search` | 忽略查询参数 |
| 带锚点 | `https://example.com/page#top` | `example.com/page` | 忽略锚点 |
| 根路径 | `https://example.com/` | `example.com` | 忽略根路径 |

## 🎉 操作界面预览

### 确认界面显示
```
📋 操作确认

URL替换信息:
  第一步 - 完整URL替换:
    旧URL: https://demo66.yhct.site
    新URL: https://preview.yhct.site/zhenglingzhineng
  第二步 - 域名替换:
    旧域名: demo66.yhct.site
    新域名: preview.yhct.site/zhenglingzhineng
    说明: 包含路径的域名替换

操作范围:
  ✓ 第一步：完整URL替换（数据库 + 文件）
  ✓ 第二步：域名替换（数据库 + 文件）
```

### 执行过程显示
```
🚀 第一步：执行完整URL替换
正在替换数据库中的完整URL...
✅ 第一步完成！

🚀 第二步：执行域名替换
正在替换数据库中的域名...
正在替换文件中的域名...
✅ 第二步完成！
```

## 💡 最佳实践

1. **测试环境验证**：在生产环境使用前，先在测试环境验证路径替换效果
2. **备份重要性**：包含路径的替换可能影响更多内容，务必创建备份
3. **路径检查**：确保新URL的路径是正确的，避免404错误
4. **相对路径考虑**：注意检查是否有相对路径引用需要手动调整

这个增强的域名提取逻辑确保了更准确、更完整的WordPress站点URL迁移！🚀
