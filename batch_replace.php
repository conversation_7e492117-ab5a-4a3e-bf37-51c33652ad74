<?php
/**
 * WordPress 批量URL替换脚本 - PHP版本
 * 用于批量处理多个WordPress站点的URL替换
 * 
 * 使用方法:
 * php batch_replace.php --list
 * php batch_replace.php --sites="vincetest,demoa" --old-pattern="http://localhost/{site}" --new-pattern="https://{site}.com"
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(0);

class BatchURLReplacer {
    
    private $base_dir = '/var/www/dev_sites';
    private $script_path = './wp_url_replacer.php';
    private $log_dir = './batch_logs';
    
    public function __construct() {
        // 创建日志目录
        if (!is_dir($this->log_dir)) {
            mkdir($this->log_dir, 0755, true);
        }
    }
    
    /**
     * 彩色输出
     */
    private function colorOutput($text, $color = 'white') {
        $colors = array(
            'red' => "\033[0;31m",
            'green' => "\033[0;32m",
            'yellow' => "\033[1;33m",
            'blue' => "\033[0;34m",
            'white' => "\033[0m"
        );
        
        $color_code = isset($colors[$color]) ? $colors[$color] : $colors['white'];
        echo $color_code . $text . $colors['white'];
    }
    
    /**
     * 显示帮助信息
     */
    public function showHelp() {
        $this->colorOutput("WordPress 批量URL替换脚本 - PHP版本\n\n", 'blue');
        echo "用法:\n";
        echo "  php batch_replace.php [选项]\n\n";
        echo "选项:\n";
        echo "  --help                  显示此帮助信息\n";
        echo "  --list                  列出所有可用的WordPress站点\n";
        echo "  --sites=SITES           指定要处理的站点（用逗号分隔）\n";
        echo "  --old-pattern=PATTERN   旧URL模式（使用 {site} 作为站点名占位符）\n";
        echo "  --new-pattern=PATTERN   新URL模式（使用 {site} 作为站点名占位符）\n";
        echo "  --db-only               只替换数据库\n";
        echo "  --files-only            只替换文件\n";
        echo "  --extensions=EXT        指定文件扩展名（用逗号分隔）\n";
        echo "  --yes                   自动确认所有操作\n\n";
        echo "示例:\n";
        echo "  php batch_replace.php --list\n";
        echo "  php batch_replace.php --sites=\"vincetest,demoa\" --old-pattern=\"http://localhost/{site}\" --new-pattern=\"https://{site}.com\"\n";
        echo "  php batch_replace.php --sites=\"vincetest\" --old-pattern=\"http://vincetest.local\" --new-pattern=\"https://vincetest.com\" --yes\n";
    }
    
    /**
     * 列出所有WordPress站点
     */
    public function listSites() {
        $this->colorOutput("扫描WordPress站点...\n\n", 'blue');
        
        $sites = array();
        if (is_dir($this->base_dir)) {
            $items = scandir($this->base_dir);
            foreach ($items as $item) {
                if ($item == '.' || $item == '..') {
                    continue;
                }
                
                $path = $this->base_dir . '/' . $item;
                if (is_dir($path) && file_exists($path . '/wp-config.php')) {
                    $sites[] = $item;
                }
            }
        }
        
        if (empty($sites)) {
            $this->colorOutput("未找到WordPress站点\n", 'yellow');
            return;
        }
        
        $this->colorOutput("找到以下WordPress站点:\n", 'green');
        foreach ($sites as $i => $site) {
            echo "  " . ($i + 1) . ". " . $site . "\n";
        }
        echo "\n";
        $this->colorOutput("总计: " . count($sites) . " 个站点\n", 'blue');
    }
    
    /**
     * 处理单个站点
     */
    private function processSite($site_name, $old_url, $new_url, $options = '') {
        $site_path = $this->base_dir . '/' . $site_name;
        $log_file = $this->log_dir . '/' . $site_name . '_' . date('Ymd_His') . '.log';
        
        $this->colorOutput("处理站点: $site_name\n", 'blue');
        echo "  站点路径: $site_path\n";
        echo "  旧URL: $old_url\n";
        echo "  新URL: $new_url\n";
        echo "  日志文件: $log_file\n";
        
        // 检查站点是否存在
        if (!is_dir($site_path)) {
            $this->colorOutput("  错误: 站点目录不存在\n", 'red');
            return false;
        }
        
        if (!file_exists($site_path . '/wp-config.php')) {
            $this->colorOutput("  错误: 不是有效的WordPress站点\n", 'red');
            return false;
        }
        
        // 执行替换
        $this->colorOutput("  开始URL替换...\n", 'yellow');
        
        $cmd = "php " . $this->script_path . " '$site_path' --old-url='$old_url' --new-url='$new_url' $options";
        
        // 执行命令并捕获输出
        ob_start();
        $return_code = 0;
        passthru($cmd . " 2>&1", $return_code);
        $output = ob_get_clean();
        
        // 保存日志
        file_put_contents($log_file, $output);
        
        if ($return_code === 0) {
            $this->colorOutput("  ✓ 替换成功\n", 'green');
            
            // 显示简要统计
            if (preg_match('/数据库记录总数: (\d+)/', $output, $matches)) {
                echo "    数据库记录总数: " . $matches[1] . "\n";
            }
            if (preg_match('/数据库替换记录数: (\d+)/', $output, $matches)) {
                echo "    数据库替换记录数: " . $matches[1] . "\n";
            }
            if (preg_match('/处理文件总数: (\d+)/', $output, $matches)) {
                echo "    处理文件总数: " . $matches[1] . "\n";
            }
            if (preg_match('/替换文件数: (\d+)/', $output, $matches)) {
                echo "    替换文件数: " . $matches[1] . "\n";
            }
            
            return true;
        } else {
            $this->colorOutput("  ✗ 替换失败\n", 'red');
            $this->colorOutput("  请查看日志文件: $log_file\n", 'yellow');
            return false;
        }
    }
    
    /**
     * 确认操作
     */
    private function confirmOperation($sites_str, $old_pattern, $new_pattern, $options, $auto_confirm) {
        $this->colorOutput("即将执行以下操作:\n", 'yellow');
        echo "  站点: $sites_str\n";
        echo "  旧URL模式: $old_pattern\n";
        echo "  新URL模式: $new_pattern\n";
        echo "  选项: $options\n\n";
        
        if (!$auto_confirm) {
            echo "确定要继续吗？(y/N): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (strtolower(trim($line)) !== 'y') {
                $this->colorOutput("操作已取消\n", 'yellow');
                exit(0);
            }
        }
    }
    
    /**
     * 批量处理
     */
    public function batchProcess($sites, $old_pattern, $new_pattern, $options = '', $auto_confirm = false) {
        $sites_array = explode(',', $sites);
        $sites_array = array_map('trim', $sites_array);
        
        // 确认操作
        $this->confirmOperation($sites, $old_pattern, $new_pattern, $options, $auto_confirm);
        
        // 开始批量处理
        $this->colorOutput("开始批量处理...\n\n", 'blue');
        
        $success_count = 0;
        $fail_count = 0;
        $start_time = time();
        
        foreach ($sites_array as $site) {
            // 替换占位符
            $old_url = str_replace('{site}', $site, $old_pattern);
            $new_url = str_replace('{site}', $site, $new_pattern);
            
            if ($this->processSite($site, $old_url, $new_url, $options)) {
                $success_count++;
            } else {
                $fail_count++;
            }
            
            echo "\n";
        }
        
        // 显示总结
        $end_time = time();
        $duration = $end_time - $start_time;
        
        $this->colorOutput("=== 批量处理完成 ===\n", 'blue');
        echo "  成功: $success_count 个站点\n";
        echo "  失败: $fail_count 个站点\n";
        echo "  总用时: $duration 秒\n";
        echo "  日志目录: " . $this->log_dir . "\n";
        
        if ($fail_count > 0) {
            $this->colorOutput("  请检查失败站点的日志文件\n", 'yellow');
            exit(1);
        } else {
            $this->colorOutput("  所有站点处理成功！\n", 'green');
            exit(0);
        }
    }
}

/**
 * 解析命令行参数
 */
function parseArgs($argv) {
    $args = array(
        'help' => false,
        'list' => false,
        'sites' => '',
        'old_pattern' => '',
        'new_pattern' => '',
        'db_only' => false,
        'files_only' => false,
        'extensions' => '',
        'yes' => false
    );

    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if ($arg == '--help') {
            $args['help'] = true;
        } elseif ($arg == '--list') {
            $args['list'] = true;
        } elseif ($arg == '--db-only') {
            $args['db_only'] = true;
        } elseif ($arg == '--files-only') {
            $args['files_only'] = true;
        } elseif ($arg == '--yes') {
            $args['yes'] = true;
        } elseif (strpos($arg, '--sites=') === 0) {
            $args['sites'] = substr($arg, 8);
        } elseif (strpos($arg, '--old-pattern=') === 0) {
            $args['old_pattern'] = substr($arg, 14);
        } elseif (strpos($arg, '--new-pattern=') === 0) {
            $args['new_pattern'] = substr($arg, 14);
        } elseif (strpos($arg, '--extensions=') === 0) {
            $args['extensions'] = substr($arg, 13);
        }
    }

    return $args;
}

/**
 * 主函数
 */
function main() {
    global $argv;

    $args = parseArgs($argv);
    $replacer = new BatchURLReplacer();

    // 显示帮助
    if ($args['help']) {
        $replacer->showHelp();
        exit(0);
    }

    // 列出站点
    if ($args['list']) {
        $replacer->listSites();
        exit(0);
    }

    // 检查必需参数
    if (empty($args['sites']) || empty($args['old_pattern']) || empty($args['new_pattern'])) {
        echo "错误: 缺少必需参数\n\n";
        $replacer->showHelp();
        exit(1);
    }

    // 构建选项
    $options = '';
    if ($args['db_only']) {
        $options .= ' --db-only';
    }
    if ($args['files_only']) {
        $options .= ' --files-only';
    }
    if (!empty($args['extensions'])) {
        $options .= ' --extensions=' . $args['extensions'];
    }

    // 执行批量处理
    $replacer->batchProcess(
        $args['sites'],
        $args['old_pattern'],
        $args['new_pattern'],
        $options,
        $args['yes']
    );
}

// 运行主函数
if (php_sapi_name() === 'cli') {
    main();
} else {
    echo "此脚本只能在命令行中运行\n";
    exit(1);
}
