#!/bin/bash
# WordPress URL替换工具安装脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== WordPress URL替换工具安装脚本 ===${NC}"
echo ""

# 检查Python3
echo -e "${YELLOW}检查Python3...${NC}"
if command -v python3 &> /dev/null; then
    python_version=$(python3 --version)
    echo -e "${GREEN}✓ 找到Python3: $python_version${NC}"
else
    echo -e "${RED}✗ 未找到Python3，请先安装Python3${NC}"
    exit 1
fi

# 检查pip3
echo -e "${YELLOW}检查pip3...${NC}"
if command -v pip3 &> /dev/null; then
    echo -e "${GREEN}✓ 找到pip3${NC}"
else
    echo -e "${RED}✗ 未找到pip3，请先安装pip3${NC}"
    exit 1
fi

# 安装Python依赖
echo -e "${YELLOW}安装Python依赖...${NC}"
if pip3 install mysql-connector-python; then
    echo -e "${GREEN}✓ Python依赖安装成功${NC}"
else
    echo -e "${RED}✗ Python依赖安装失败${NC}"
    exit 1
fi

# 设置脚本权限
echo -e "${YELLOW}设置脚本权限...${NC}"
chmod +x wp_url_replacer.py
chmod +x batch_replace.sh

if [ -x wp_url_replacer.py ] && [ -x batch_replace.sh ]; then
    echo -e "${GREEN}✓ 脚本权限设置成功${NC}"
else
    echo -e "${RED}✗ 脚本权限设置失败${NC}"
    exit 1
fi

# 创建必要目录
echo -e "${YELLOW}创建必要目录...${NC}"
mkdir -p batch_logs
mkdir -p backups

if [ -d batch_logs ] && [ -d backups ]; then
    echo -e "${GREEN}✓ 目录创建成功${NC}"
else
    echo -e "${RED}✗ 目录创建失败${NC}"
    exit 1
fi

# 测试工具
echo -e "${YELLOW}测试工具...${NC}"
if python3 wp_url_replacer.py --help > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Python脚本测试通过${NC}"
else
    echo -e "${RED}✗ Python脚本测试失败${NC}"
    exit 1
fi

if ./batch_replace.sh --help > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 批量脚本测试通过${NC}"
else
    echo -e "${RED}✗ 批量脚本测试失败${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== 安装完成 ===${NC}"
echo ""
echo -e "${BLUE}使用方法:${NC}"
echo "1. 交互式模式:"
echo "   python3 wp_url_replacer.py /var/www/dev_sites/站点名"
echo ""
echo "2. 命令行模式:"
echo "   python3 wp_url_replacer.py /var/www/dev_sites/站点名 --old-url '旧URL' --new-url '新URL'"
echo ""
echo "3. 批量处理:"
echo "   ./batch_replace.sh --list  # 列出所有站点"
echo "   ./batch_replace.sh -s '站点1,站点2' -o 'http://localhost/{site}' -n 'https://{site}.com'"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "- 使用前请务必备份数据库和文件"
echo "- 建议先在测试环境中验证"
echo "- 查看 README_URL_REPLACER.md 获取详细说明"
echo ""
echo -e "${GREEN}安装成功！${NC}"
