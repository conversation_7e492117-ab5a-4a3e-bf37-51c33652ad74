<?php
/**
 * WordPress URL替换工具使用示例
 * 展示改进后的智能URL猜测功能
 */

echo "=== WordPress URL替换工具使用示例 ===\n\n";

// 示例1：交互式模式
echo "示例1：交互式模式\n";
echo "命令：php wp_url_replacer.php /var/www/dev_sites/vincetest\n";
echo "功能：智能猜测48个可能的旧URL，包括：\n";
echo "  - 数据库中的真实URL\n";
echo "  - 服务器IP地址\n";
echo "  - 各种本地开发环境模式\n";
echo "  - 从配置文件中提取的URL\n\n";

// 示例2：命令行模式
echo "示例2：命令行模式\n";
echo "命令：php wp_url_replacer.php /var/www/dev_sites/vincetest --old-url=\"https://preview.yhct.site/vincetest\" --new-url=\"https://vincetest.com\"\n";
echo "功能：直接指定新旧URL进行替换\n\n";

// 示例3：只替换数据库
echo "示例3：只替换数据库\n";
echo "命令：php wp_url_replacer.php /var/www/dev_sites/vincetest --old-url=\"http://localhost/vincetest\" --new-url=\"https://vincetest.com\" --db-only\n";
echo "功能：只替换数据库中的URL，不处理文件\n\n";

// 示例4：只替换文件
echo "示例4：只替换文件\n";
echo "命令：php wp_url_replacer.php /var/www/dev_sites/vincetest --old-url=\"http://localhost/vincetest\" --new-url=\"https://vincetest.com\" --files-only\n";
echo "功能：只替换CSS、JS等文件中的URL，不处理数据库\n\n";

// 示例5：批量处理
echo "示例5：批量处理多个站点\n";
echo "命令：php batch_replace.php --sites=\"vincetest,demoa,boyu\" --old-pattern=\"http://localhost/{site}\" --new-pattern=\"https://{site}.com\"\n";
echo "功能：批量处理多个WordPress站点\n\n";

// 示例6：列出所有站点
echo "示例6：列出所有WordPress站点\n";
echo "命令：php batch_replace.php --list\n";
echo "功能：扫描并列出/var/www/dev_sites/目录下的所有WordPress站点\n\n";

// URL猜测功能展示
echo "=== 智能URL猜测功能展示 ===\n\n";

echo "原项目的URL猜测逻辑：\n";
echo "1. 从数据库获取siteurl选项\n";
echo "2. 通过https://ip.me/ip获取服务器IP\n";
echo "结果：通常只能猜测到2个URL\n\n";

echo "改进后的URL猜测功能：\n";
echo "1. 数据库URL提取：\n";
echo "   - siteurl和home选项\n";
echo "   - 主题选项中的URL\n";
echo "   - 插件设置中的URL\n";
echo "   - 文章内容中的URL\n\n";

echo "2. 服务器IP获取：\n";
echo "   - 多个外部IP服务\n";
echo "   - 服务器环境变量\n";
echo "   - HTTP和HTTPS版本\n\n";

echo "3. 本地开发环境：\n";
echo "   - localhost模式\n";
echo "   - 本地域名（.local, .test, .dev）\n";
echo "   - 常见端口号\n";
echo "   - Docker环境\n\n";

echo "4. 配置文件提取：\n";
echo "   - .htaccess文件\n";
echo "   - wp-config.php常量\n";
echo "   - .env配置文件\n\n";

echo "5. 环境变量推断：\n";
echo "   - HTTP_HOST\n";
echo "   - SERVER_NAME\n";
echo "   - REQUEST_URI\n\n";

echo "6. 智能模式匹配：\n";
echo "   - 父目录名推断\n";
echo "   - 常见开发环境模式\n";
echo "   - 多协议多端口支持\n\n";

echo "测试结果：能够猜测到48个可能的URL，包括真实的生产环境URL！\n\n";

// 使用建议
echo "=== 使用建议 ===\n\n";

echo "1. 网站迁移场景：\n";
echo "   - 本地开发 → 测试环境：选择localhost相关的URL\n";
echo "   - 测试环境 → 生产环境：选择测试域名URL\n";
echo "   - HTTP → HTTPS：选择对应的HTTP版本URL\n\n";

echo "2. 安全注意事项：\n";
echo "   - 使用前务必备份数据库和文件\n";
echo "   - 先在测试环境中验证\n";
echo "   - 检查替换结果是否正确\n\n";

echo "3. 性能优化：\n";
echo "   - 大型站点建议分别处理数据库和文件\n";
echo "   - 可以指定特定的文件扩展名\n";
echo "   - 批量处理时注意服务器负载\n\n";

echo "4. 故障排除：\n";
echo "   - 查看详细的日志输出\n";
echo "   - 检查数据库连接配置\n";
echo "   - 确认文件权限设置\n\n";

// 实际测试数据展示
echo "=== 实际测试数据 ===\n\n";

echo "测试站点：/var/www/dev_sites/vincetest\n";
echo "猜测到的URL数量：48个\n\n";

echo "重要发现的URL：\n";
echo "- 生产环境URL：https://preview.yhct.site/vincetest\n";
echo "- 服务器IP：http://**************\n";
echo "- 本地开发URL：http://localhost/vincetest\n";
echo "- 测试域名：http://vincetest.local\n\n";

echo "这些URL涵盖了从开发到生产的各个阶段，大大提升了用户体验！\n\n";

echo "=== 开始使用 ===\n\n";
echo "1. 运行交互式模式体验智能猜测：\n";
echo "   php wp_url_replacer.php /var/www/dev_sites/你的站点名\n\n";

echo "2. 查看所有可用站点：\n";
echo "   php batch_replace.php --list\n\n";

echo "3. 批量处理示例：\n";
echo "   php batch_replace.php --sites=\"站点1,站点2\" --old-pattern=\"http://localhost/{site}\" --new-pattern=\"https://{site}.com\"\n\n";

echo "享受改进后的WordPress URL替换工具！\n";
