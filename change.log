# WordPress URL替换工具 - 更新日志

## v2.5 (2025-07-29) - 修复内存耗尽问题 🔧

### 🔧 内存管理优化

#### 核心问题修复
- **修复内存泄漏**：修复数据库查询结果未释放导致的内存耗尽问题
- **增加内存限制**：将PHP内存限制从512M提升到1024M
- **添加垃圾回收**：在大数据处理循环中定期进行垃圾回收

#### 具体修复内容
- **查询结果释放**：在所有数据库查询后添加 `$result->free()` 释放内存
  - 修复位置：第270行、第1568行、第1578行、第1726行、第1741行
- **循环优化**：在主要数据处理循环中每10页进行一次垃圾回收
- **内存监控**：提升内存限制以处理大型数据库
- **查询优化**：将每次查询行数从10000减少到1000，降低单次内存占用

#### 影响范围
- **解决Fatal Error**：修复 "Allowed memory size exhausted" 错误
- **提升稳定性**：大型数据库处理更加稳定可靠
- **性能优化**：减少内存占用，提高处理效率

## v2.4 (2025-01-18) - 优化用户确认默认行为 ⚡

### ⚡ 用户体验优化

#### 确认步骤默认行为调整
- **数据库备份选项**：默认行为从"不备份"改为"创建备份"
  - 提示文字：`是否在操作前创建数据库备份？(Y/n):`
  - 默认行为：用户按回车 = 自动创建备份（更安全）

- **最终操作确认**：默认行为从"取消操作"改为"确认执行"
  - 提示文字：`确定要执行URL替换操作吗？此操作将修改数据库和文件！(Y/n):`
  - 默认行为：用户按回车 = 自动确认执行（更高效）

#### 用户体验改进
- **快速确认流程**：用户可通过连续按两次回车快速完成所有确认
- **安全性提升**：默认创建备份，降低数据丢失风险
- **效率提升**：减少用户输入，提高操作效率
- **保持灵活性**：明确输入 'n' 仍可拒绝任何操作

#### 使用场景优化
```
修改前（需要明确输入）：
  备份选项：输入 y → 创建备份
  操作确认：输入 y → 执行操作

修改后（快速确认）：
  备份选项：按回车 → 自动创建备份 ✅
  操作确认：按回车 → 自动确认执行 ✅

拒绝操作（保持不变）：
  备份选项：输入 n → 跳过备份
  操作确认：输入 n → 取消操作
```

## v2.3 (2025-01-18) - 增强域名提取逻辑 🔧

### 🔧 域名提取逻辑增强

#### 核心问题修复
- **修复包含路径的URL处理**：解决新URL包含路径时第二步替换不完整的问题
- **增强extractDomain()方法**：现在提取 `host + port + path` 而不仅仅是 `host`
- **完整路径保留**：确保 `preview.yhct.site/zhenglingzhineng` 这样的URL能正确处理

#### 功能改进
- **端口号支持**：正确处理包含端口的URL（如 `localhost:8080`）
- **复杂路径支持**：支持多级路径和特殊字符路径
- **查询参数过滤**：自动忽略URL中的查询参数和锚点
- **根路径智能处理**：自动忽略单独的根路径 `/`

#### 测试验证
- **12/12 测试用例通过**：涵盖各种URL格式和边界情况
- **实际场景验证**：用户报告的问题场景完全修复
- **向后兼容性确认**：不影响现有的纯域名替换功能

#### 使用示例
```
修复前：
  旧URL: https://demo66.yhct.site
  新URL: https://preview.yhct.site/zhenglingzhineng
  第二步: demo66.yhct.site → preview.yhct.site (❌ 丢失路径)

修复后：
  旧URL: https://demo66.yhct.site
  新URL: https://preview.yhct.site/zhenglingzhineng
  第二步: demo66.yhct.site → preview.yhct.site/zhenglingzhineng (✅ 保留路径)
```

## v2.2 (2025-01-17) - 多步骤URL替换功能 🎯

### 🆕 多步骤URL替换功能

#### 核心功能增强
- **两步替换策略**：实现更彻底的URL替换，确保不遗漏任何引用
- **第一步 - 完整URL替换**：替换带协议的完整URL（如 https://www.123.com → https://www.321.com）
- **第二步 - 域名替换**：替换不带协议的域名引用（如 www.123.com → www.321.com）
- **智能域名提取**：自动从用户输入的URL中提取域名部分
- **条件执行**：只有当域名不同时才执行第二步替换

#### 用户体验改进
- **操作预览增强**：在确认界面显示两步替换的详细信息
- **分步进度显示**：清晰显示每一步的执行进度和状态
- **详细统计信息**：分别显示每步的替换统计和总计信息
- **历史记录增强**：操作历史包含多步骤替换的完整信息

#### 技术实现
- **保持向后兼容**：现有的单步替换逻辑完全保留
- **统计信息重构**：支持分步统计和总计统计
- **日志记录增强**：详细记录两步替换的执行过程
- **错误处理完善**：每步都有独立的错误处理和恢复机制

## v2.1 (2025-01-17) - 完全独立的统一URL替换脚本 🚀

### 🎯 重大架构升级：完全独立化

#### 核心功能迁移（方案B实施）
- **完整迁移DomainNameChanger类**：将wp_url_replacer.php中的400行核心类完整迁移
- **完整迁移序列化处理函数**：包含所有递归序列化/反序列化逻辑
- **完整迁移文件URL替换逻辑**：包含目录扫描、文件过滤、内容替换
- **完整迁移数据库URL替换逻辑**：包含数据库连接、表遍历、事务处理
- **完整迁移URL猜测功能**：包含数据库URL提取、服务器IP检测、环境推断

#### 独立性验证
- **✅ 5/5满分独立性评估**：通过完整的独立性测试
- **✅ 无外部依赖**：不再依赖wp_url_replacer.php或任何其他文件
- **✅ 完整功能**：包含原有的所有核心功能
- **✅ 1637行代码**：包含1169行有效代码，227行注释

### 🆕 新增功能

#### 1. 统一交互式脚本 (interactive_url_replacer.php)
- **创建全新的统一脚本**：整合现有的wp_url_replacer.php功能
- **完整的用户交互界面**：彩色输出、进度显示、友好的用户体验
- **模块化设计**：清晰的功能分离和代码组织

#### 2. 站点发现功能
- **智能站点扫描**：自动扫描指定目录下的WordPress站点
- **多目录支持**：支持用户输入多个扫描根目录
- **站点验证**：检测wp-config.php文件确保站点有效性
- **交互式选择**：提供清晰的站点列表供用户选择
- **默认路径支持**：提供/var/www/dev_sites/作为默认扫描路径

#### 3. 增强的URL猜测与选择
- **基于现有逻辑**：整合wp_url_replacer.php中的智能URL猜测功能
- **优先级显示**：显示URL来源和优先级信息
- **自定义输入选项**：提供"自定义输入URL"选项
- **URL验证**：完整的URL格式验证和重复检查
- **精准猜测**：基于数据库、服务器IP、开发环境等多源猜测

#### 4. 操作确认与安全功能
- **详细操作预览**：显示站点信息、URL替换详情、操作范围
- **多重确认机制**：操作前的明确确认步骤
- **数据库备份选项**：可选的自动数据库备份功能
- **备份验证**：检查备份文件创建状态
- **操作取消**：任何阶段都可以安全取消操作

#### 5. 日志记录与历史管理
- **实时日志记录**：所有操作的详细日志记录
- **操作历史保存**：JSON格式的操作历史存储
- **历史查看功能**：查看所有历史操作的详细信息
- **状态跟踪**：记录操作状态、用时、备份文件等信息

#### 6. 回滚功能
- **智能回滚检测**：自动识别可回滚的操作（需要备份文件）
- **回滚操作选择**：提供历史操作列表供选择回滚
- **安全回滚确认**：回滚前的详细确认和警告
- **数据库恢复**：基于备份文件的完整数据库恢复

#### 7. 用户体验增强
- **彩色输出界面**：使用ANSI颜色代码提供友好的视觉体验
- **清晰的界面布局**：使用边框、分隔线等提升可读性
- **进度反馈**：实时显示操作进度和状态
- **错误处理**：完善的错误处理和用户友好的错误信息
- **多选项菜单**：完成后提供多种后续操作选项
- **改进的输入体验**：支持readline扩展，提供方向键、退格键、历史记录等功能
- **修复URL猜测显示**：修复来源信息显示为"unknown"的问题，提供准确的来源和优先级信息
- **修复MySQL严格模式兼容性**：解决'0000-00-00 00:00:00'无效日期导致的错误

### 🔧 技术改进

#### 1. 代码架构优化
- **面向对象设计**：InteractiveURLReplacer类的完整实现
- **功能模块化**：将复杂功能拆分为独立的方法
- **代码复用**：充分利用现有的WordPressURLReplacer类
- **错误处理**：完善的异常处理机制
- **模块隔离**：解决wp_url_replacer.php主程序执行冲突问题
- **数据库兼容性增强**：添加MySQL严格模式兼容性，自动修复无效日期数据

#### 2. 数据管理
- **配置访问**：添加getDbConfig()方法到WordPressURLReplacer类
- **历史数据**：JSON格式的操作历史持久化存储
- **备份管理**：自动化的数据库备份和恢复流程
- **文件组织**：清晰的日志、备份、历史文件组织结构

#### 3. 安全性增强
- **输入验证**：严格的URL格式验证和路径检查
- **操作确认**：多重确认机制防止误操作
- **备份保护**：操作前自动备份重要数据
- **回滚支持**：提供完整的操作回滚能力

### 📁 文件结构变更

#### 新增文件
- `interactive_url_replacer.php` - 统一的交互式URL替换脚本
- `change.log` - 详细的更新日志文件
- `backups/` - 自动创建的备份目录
- `operation_history.json` - 操作历史记录文件
- `interactive_replacer.log` - 交互式脚本的日志文件

#### 修改文件
- `wp_url_replacer.php` - 添加getDbConfig()公共方法，修复主程序执行冲突，修复URL猜测来源显示问题

### 🎯 使用方法

#### 基本使用
```bash
# 启动交互式URL替换脚本
php interactive_url_replacer.php
```

#### 功能流程
1. **站点发现**：输入根目录路径，自动扫描WordPress站点
2. **站点选择**：从发现的站点列表中选择目标站点
3. **URL猜测**：系统智能猜测可能的旧URL
4. **URL选择**：选择猜测的URL或自定义输入
5. **新URL输入**：输入目标新URL
6. **操作确认**：查看操作详情并确认
7. **备份选择**：选择是否创建数据库备份
8. **执行替换**：执行URL替换操作
9. **后续选项**：处理新站点、查看历史、回滚等

### 🔒 安全特性

- **数据备份**：操作前可选的自动数据库备份
- **操作确认**：多重确认机制防止误操作
- **回滚支持**：基于备份的完整回滚功能
- **日志记录**：详细的操作日志和历史记录
- **错误处理**：完善的错误处理和恢复机制

### 📊 兼容性

- **向后兼容**：完全兼容现有的wp_url_replacer.php功能
- **独立运行**：可以独立使用，不影响现有工具
- **集成友好**：可以与现有的批量处理脚本配合使用

---

## v1.2 (2024-07-16) - 精准智能猜测
- 🎯 重新设计URL猜测逻辑，从48个混乱URL优化到10个精准URL
- 📊 实现优先级系统和相关性过滤
- 🥇 最重要的URL（数据库真实URL）排在第一位
- 👥 解决用户体验问题，提供真正实用的功能

## v1.1 (2024-07-16) - 功能扩展
- ✨ 创建PHP版本的独立URL替换工具
- 🚀 智能URL猜测功能大幅增强
- 📦 支持批量处理多个站点
- 🔍 多数据源URL提取
- 🛡️ 增强的安全和错误处理

## v1.0 - 原始版本
- 基础URL替换功能
- WordPress插件集成
- 简单的URL猜测

---

**注意事项：**
- 使用前请务必备份重要数据
- 建议在测试环境中先行验证
- 操作过程中请仔细阅读确认信息
- 如遇问题可查看日志文件进行排查
