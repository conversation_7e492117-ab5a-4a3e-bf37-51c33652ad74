<?php
/**
 * URL获取来源详细解释
 * 展示每个URL是如何获取的
 */

echo "=== WordPress URL获取来源详细解释 ===\n\n";

echo "📍 测试站点：/var/www/dev_sites/vincetest\n";
echo "🔍 获取到的10个URL及其来源：\n\n";

// URL来源分析
$url_sources = array(
    array(
        'url' => 'https://preview.yhct.site/vincetest',
        'source' => '数据库 wp_options 表',
        'method' => 'database_siteurl',
        'priority' => 10,
        'sql' => "SELECT option_value FROM wp_options WHERE option_name='siteurl'",
        'explanation' => '这是WordPress数据库中存储的站点URL，通常是网站的真实访问地址'
    ),
    array(
        'url' => 'http://localhost/vincetest',
        'source' => '本地开发环境推断',
        'method' => 'development_localhost',
        'priority' => 9,
        'sql' => null,
        'explanation' => '基于站点目录名(vincetest)生成的最常见本地开发URL模式'
    ),
    array(
        'url' => 'http://**************',
        'source' => '外部IP服务',
        'method' => 'server_ip_http',
        'priority' => 8,
        'sql' => null,
        'explanation' => '通过访问 https://ip.me/ip 获取服务器公网IP地址'
    ),
    array(
        'url' => 'https://localhost/vincetest',
        'source' => '本地开发环境推断',
        'method' => 'development_localhost_https',
        'priority' => 8,
        'sql' => null,
        'explanation' => '基于站点目录名生成的HTTPS版本本地开发URL'
    ),
    array(
        'url' => 'https://**************',
        'source' => '外部IP服务',
        'method' => 'server_ip_https',
        'priority' => 7,
        'sql' => null,
        'explanation' => '服务器IP的HTTPS版本'
    ),
    array(
        'url' => 'http://vincetest.local',
        'source' => '本地域名推断',
        'method' => 'development_local_domain',
        'priority' => 7,
        'sql' => null,
        'explanation' => '基于站点名生成的.local域名，常用于本地开发环境'
    ),
    array(
        'url' => 'https://vincetest.local',
        'source' => '本地域名推断',
        'method' => 'development_local_domain_https',
        'priority' => 6,
        'sql' => null,
        'explanation' => '.local域名的HTTPS版本'
    ),
    array(
        'url' => 'http://vincetest.test',
        'source' => '测试域名推断',
        'method' => 'development_test_domain',
        'priority' => 6,
        'sql' => null,
        'explanation' => '基于站点名生成的.test域名，常用于测试环境'
    ),
    array(
        'url' => 'http://localhost:8080/vincetest',
        'source' => '常见端口推断',
        'method' => 'development_port_8080',
        'priority' => 5,
        'sql' => null,
        'explanation' => '8080端口的本地开发环境，常见于Java应用或代理服务器'
    ),
    array(
        'url' => 'http://localhost:8000/vincetest',
        'source' => '常见端口推断',
        'method' => 'development_port_8000',
        'priority' => 5,
        'sql' => null,
        'explanation' => '8000端口的本地开发环境，常见于Python Django或其他框架'
    )
);

foreach ($url_sources as $index => $item) {
    $num = $index + 1;
    echo "🔸 URL #{$num}: {$item['url']}\n";
    echo "   📍 来源: {$item['source']}\n";
    echo "   🔧 获取方法: {$item['method']}\n";
    echo "   ⭐ 优先级: {$item['priority']}/10\n";
    if ($item['sql']) {
        echo "   💾 SQL查询: {$item['sql']}\n";
    }
    echo "   💡 说明: {$item['explanation']}\n\n";
}

echo "=== 详细获取逻辑 ===\n\n";

echo "1️⃣ **数据库URL获取** (getUrlsFromDatabase方法):\n";
echo "   - 连接WordPress数据库\n";
echo "   - 查询wp_options表中的siteurl和home选项\n";
echo "   - SQL: SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home')\n";
echo "   - 结果: https://preview.yhct.site/vincetest (来自siteurl)\n\n";

echo "2️⃣ **服务器IP获取** (getServerIpUrls方法):\n";
echo "   - 访问外部IP服务: https://ip.me/ip\n";
echo "   - 备用服务: https://ipinfo.io/ip\n";
echo "   - 验证返回的IP地址格式\n";
echo "   - 生成HTTP和HTTPS两个版本\n";
echo "   - 结果: http://************** 和 https://**************\n\n";

echo "3️⃣ **本地开发环境推断** (guessRelevantDevelopmentUrls方法):\n";
echo "   - 获取站点目录名: basename('/var/www/dev_sites/vincetest') = 'vincetest'\n";
echo "   - 生成常见的本地开发URL模式:\n";
echo "     * http://localhost/vincetest (最常见)\n";
echo "     * https://localhost/vincetest (HTTPS版本)\n";
echo "     * http://vincetest.local (本地域名)\n";
echo "     * https://vincetest.local (HTTPS本地域名)\n";
echo "     * http://vincetest.test (测试域名)\n";
echo "     * http://localhost:8080/vincetest (常见端口)\n";
echo "     * http://localhost:8000/vincetest (常见端口)\n\n";

echo "4️⃣ **优先级排序逻辑** (filterAndRankUrls方法):\n";
echo "   - 数据库URL: 优先级10 (最高)\n";
echo "   - 本地开发localhost: 优先级9\n";
echo "   - 服务器IP HTTP: 优先级8\n";
echo "   - 本地开发HTTPS: 优先级8\n";
echo "   - 服务器IP HTTPS: 优先级7\n";
echo "   - 本地域名: 优先级7-6\n";
echo "   - 常见端口: 优先级5\n\n";

echo "5️⃣ **相关性过滤逻辑** (isRelevantUrl方法):\n";
echo "   ✅ 保留的URL类型:\n";
echo "   - 包含站点名 'vincetest' 的URL\n";
echo "   - 本地开发环境URL (localhost, 127.0.0.1)\n";
echo "   - 本地域名 (.local, .test, .dev)\n";
echo "   - 服务器IP地址\n\n";
echo "   ❌ 过滤掉的URL类型:\n";
echo "   - API服务 (api.*, googleapis.com)\n";
echo "   - 文档网站 (wordpress.org, w3.org)\n";
echo "   - 社交媒体 (facebook.com, twitter.com)\n";
echo "   - CDN服务 (cdn.*, gravatar.com)\n\n";

echo "=== 技术实现细节 ===\n\n";

echo "🔧 **数据库连接**:\n";
echo "   - 从wp-config.php解析数据库配置\n";
echo "   - 使用mysqli连接数据库\n";
echo "   - 设置字符集为utf8mb4\n\n";

echo "🌐 **外部IP获取**:\n";
echo "   - 设置3秒超时\n";
echo "   - 使用stream_context_create配置HTTP请求\n";
echo "   - 验证返回的IP格式 (filter_var with FILTER_VALIDATE_IP)\n";
echo "   - 失败时尝试备用服务\n\n";

echo "📊 **数据结构**:\n";
echo "   每个URL存储为数组:\n";
echo "   array(\n";
echo "       'url' => 'https://example.com',\n";
echo "       'source' => 'database_siteurl',\n";
echo "       'priority' => 10\n";
echo "   )\n\n";

echo "🎯 **最终处理**:\n";
echo "   1. 去重 (array_unique by URL)\n";
echo "   2. 按优先级排序 (uasort)\n";
echo "   3. 返回URL字符串数组\n";
echo "   4. 限制数量在合理范围内\n\n";

echo "💡 **设计理念**:\n";
echo "   - 精准性优于完整性\n";
echo "   - 最可能的URL排在前面\n";
echo "   - 覆盖常见的开发和部署场景\n";
echo "   - 用户友好的数量和排序\n\n";

echo "这就是10个URL的完整获取逻辑！每个URL都有明确的来源和用途。🎉\n";
