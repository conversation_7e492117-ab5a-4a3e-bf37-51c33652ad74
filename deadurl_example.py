#!/usr/bin/env python3
"""
死链检测脚本使用示例
配合URL替换脚本使用，检测网站中的失效链接
"""

import os
import sys
from datetime import datetime

# 检查依赖
try:
    from requests_html import HTMLSession
    print("✅ requests_html 模块已安装")
except ImportError:
    print("❌ 缺少 requests_html 模块")
    print("请运行: pip3 install requests-html")
    sys.exit(1)

# 导入死链检测类
from deadurl import BrokenLinks, genReport

def main():
    print("=== 死链检测工具 ===\n")
    
    # 配置参数
    configs = {
        # 基本配置
        'root': 'https://example.com',  # 要检测的网站根URL
        'concurrency': 5,               # 并发线程数
        'depth': 2,                     # 爬取深度
        'max_pages': 100,               # 最大页面数
        'max_tries': 3,                 # 最大重试次数
        'timeout': 10,                  # 请求超时时间（秒）
        'proxy': ()                     # 代理设置（如果需要）
    }
    
    # 获取用户输入
    root_url = input(f"请输入要检测的网站URL [默认: {configs['root']}]: ").strip()
    if root_url:
        configs['root'] = root_url
    
    print(f"\n🔍 开始检测网站: {configs['root']}")
    print(f"配置参数:")
    print(f"  - 并发数: {configs['concurrency']}")
    print(f"  - 深度: {configs['depth']}")
    print(f"  - 最大页面: {configs['max_pages']}")
    print(f"  - 超时: {configs['timeout']}秒")
    
    # 创建检测器
    checker = BrokenLinks(**configs)
    
    # 开始检测
    print(f"\n🚀 开始检测...")
    start_time = datetime.now()
    
    try:
        checker.check()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"✅ 检测完成，用时: {duration:.2f}秒")
        
        # 获取结果
        results = checker.result()
        
        if results:
            print(f"\n⚠️  发现 {len(results)} 个死链:")
            for broken_url, pages in results.items():
                print(f"  - {broken_url}")
                if pages:
                    print(f"    出现在: {', '.join(pages[:3])}{'...' if len(pages) > 3 else ''}")
            
            # 生成报告
            report_dir = './reports'
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            site_name = configs['root'].replace('https://', '').replace('http://', '').replace('/', '_')
            report_name = f"deadlinks_{site_name}_{timestamp}"
            
            filename = genReport(report_name, results, report_dir)
            print(f"\n📊 报告已生成: {report_dir}/{filename}")
            
        else:
            print(f"\n🎉 太好了！未发现死链")
            
        # 显示统计信息
        print(f"\n📈 检测统计:")
        print(f"  - 成功访问: {len(checker._success)} 个页面")
        print(f"  - 失败链接: {len(checker._failed)} 个")
        print(f"  - 总计检查: {len(checker._seen)} 个链接")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  检测被用户中断")
    except Exception as e:
        print(f"\n❌ 检测过程中发生错误: {e}")

def check_dependencies():
    """检查依赖是否安装"""
    dependencies = [
        'requests_html',
        'requests',
        'pyquery',
        'lxml'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep}")
            missing.append(dep)
    
    if missing:
        print(f"\n缺少依赖，请安装:")
        print(f"pip3 install {' '.join(missing)}")
        return False
    
    return True

def install_dependencies():
    """自动安装依赖"""
    print("🔧 正在安装依赖...")
    
    import subprocess
    
    dependencies = ['requests-html']
    
    for dep in dependencies:
        try:
            print(f"安装 {dep}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', dep
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {dep} 安装成功")
            else:
                print(f"❌ {dep} 安装失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 安装 {dep} 时发生错误: {e}")

if __name__ == "__main__":
    print("=== 死链检测工具依赖检查 ===\n")
    
    if not check_dependencies():
        choice = input("\n是否自动安装缺少的依赖？(y/N): ").strip().lower()
        if choice == 'y':
            install_dependencies()
            print("\n请重新运行脚本")
        else:
            print("请手动安装依赖后重新运行")
        sys.exit(1)
    
    print("\n✅ 所有依赖已安装，开始检测\n")
    main()
