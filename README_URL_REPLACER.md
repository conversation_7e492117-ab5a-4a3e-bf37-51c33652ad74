# WordPress URL 替换工具

这是一个Python脚本，用于批量替换WordPress站点中的URL，支持数据库和静态文件的URL替换。

## 功能特性

### 🔍 智能URL猜测
- 从WordPress数据库中自动获取站点URL配置
- 基于站点目录名生成常见的本地开发环境URL
- 从.htaccess文件中提取可能的URL
- 完全离线工作，无需联网获取IP

### 🗄️ 数据库URL替换
- 支持所有WordPress数据库表的URL替换
- 智能处理序列化数据（PHP serialize格式）
- 支持JSON格式数据的URL替换
- 分页处理大数据量，避免内存溢出
- 安全的事务处理，每次只更新一条记录

### 📁 文件URL替换
- 支持多种文件格式：CSS、JS、HTML、PHP、JSON、XML等
- 递归处理整个站点目录
- 自动排除不必要的目录（node_modules、.git等）
- 跳过过大文件，避免处理二进制文件

### 📊 详细统计信息
- 实时显示处理进度
- 详细的替换统计报告
- 完整的日志记录
- 性能监控（处理时间、内存使用等）

## 安装要求

```bash
pip install mysql-connector-python
```

## 使用方法

### 1. 交互式模式（推荐）

```bash
python wp_url_replacer.py /var/www/dev_sites/vincetest
```

交互式模式会：
1. 自动解析wp-config.php获取数据库配置
2. 智能猜测可能的旧URL并提供选择
3. 引导用户输入新URL
4. 确认后执行替换操作

### 2. 命令行模式

```bash
# 完整替换（数据库+文件）
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://localhost/vincetest" \
  --new-url "https://vincetest.com"

# 只替换数据库
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://localhost/vincetest" \
  --new-url "https://vincetest.com" \
  --db-only

# 只替换文件
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://localhost/vincetest" \
  --new-url "https://vincetest.com" \
  --files-only

# 指定文件扩展名
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://localhost/vincetest" \
  --new-url "https://vincetest.com" \
  --extensions "css,js,php"
```

### 3. 批量处理多个站点

```bash
#!/bin/bash
# 批量处理脚本示例

sites=(
    "vincetest"
    "demoa" 
    "boyu"
    "shenghe"
)

for site in "${sites[@]}"; do
    echo "处理站点: $site"
    python wp_url_replacer.py "/var/www/dev_sites/$site" \
      --old-url "http://localhost/$site" \
      --new-url "https://$site.com"
    echo "完成站点: $site"
    echo "------------------------"
done
```

## 智能URL猜测功能

脚本会自动猜测以下类型的旧URL：

### 1. 数据库配置URL
- 从`wp_options`表的`siteurl`和`home`选项获取

### 2. 本地开发环境URL模式
基于站点目录名生成：
- `http://localhost/站点名`
- `https://localhost/站点名`
- `http://127.0.0.1/站点名`
- `http://站点名.local`
- `https://站点名.local`
- `http://站点名.test`
- `https://站点名.test`
- `http://站点名.dev`
- `https://站点名.dev`

### 3. .htaccess文件URL
- 从.htaccess文件中提取的URL配置

## 安全特性

### 🔒 数据安全
- 自动备份功能（建议使用前手动备份）
- 事务安全，每次只更新一条记录
- 详细的操作日志记录
- 错误处理和回滚机制

### 🛡️ 文件安全
- 自动跳过二进制文件
- 排除系统目录和缓存目录
- 文件大小限制（默认10MB）
- UTF-8编码处理，避免乱码

## 配置选项

### 支持的文件扩展名
默认处理以下文件类型：
- `.css` - 样式表文件
- `.js` - JavaScript文件
- `.html`, `.htm` - HTML文件
- `.php` - PHP文件
- `.json` - JSON配置文件
- `.xml` - XML文件

### 排除的目录
自动排除以下目录：
- `node_modules` - Node.js依赖
- `.git` - Git版本控制
- `.svn` - SVN版本控制
- `__pycache__` - Python缓存
- `vendor` - Composer依赖
- `cache` - 缓存目录
- `logs` - 日志目录
- `tmp`, `temp` - 临时目录

## 日志和监控

### 日志文件
- 文件名：`wp_url_replacer.log`
- 编码：UTF-8
- 内容：详细的操作记录和错误信息

### 统计信息
- 数据库记录总数和替换数
- 文件处理总数和替换数
- 总处理时间
- 内存使用情况

## 使用示例

### 示例1：本地开发环境迁移到生产环境
```bash
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://localhost/vincetest" \
  --new-url "https://www.vincetest.com"
```

### 示例2：HTTP升级到HTTPS
```bash
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "http://vincetest.com" \
  --new-url "https://vincetest.com"
```

### 示例3：域名更换
```bash
python wp_url_replacer.py /var/www/dev_sites/vincetest \
  --old-url "https://old-domain.com" \
  --new-url "https://new-domain.com"
```

## 注意事项

### ⚠️ 使用前必读
1. **备份数据**：使用前请务必备份数据库和文件
2. **测试环境**：建议先在测试环境中验证
3. **权限检查**：确保脚本有读写数据库和文件的权限
4. **URL格式**：确保输入的URL格式正确，不要包含尾部斜杠

### 🔧 故障排除
1. **数据库连接失败**：检查wp-config.php配置和数据库服务状态
2. **文件权限错误**：确保脚本有足够的文件系统权限
3. **编码问题**：脚本使用UTF-8编码，确保文件编码一致
4. **内存不足**：对于大型站点，可能需要调整PHP内存限制

## 更新日志

### v1.0 (2024-07-16)
- 初始版本发布
- 支持数据库和文件URL替换
- 智能URL猜测功能
- 交互式和命令行模式
- 完整的日志和统计功能

## 技术支持

如有问题或建议，请查看日志文件 `wp_url_replacer.log` 获取详细错误信息。

## 许可证

本工具仅供学习和内部使用，请遵守相关法律法规。
