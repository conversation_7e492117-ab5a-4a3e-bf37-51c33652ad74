# 多步骤URL替换功能使用示例

## 🎯 功能概述

新版本的 `interactive_url_replacer.php` 现在支持**多步骤URL替换**，确保更彻底的URL替换，不遗漏任何引用。

### 两步替换策略：

1. **第一步：完整URL替换**
   - 替换带协议的完整URL
   - 例如：`https://www.123.com` → `https://www.321.com`

2. **第二步：域名替换**
   - 替换不带协议的域名引用
   - 例如：`www.123.com` → `www.321.com`

## 🚀 使用示例

### 启动脚本
```bash
php interactive_url_replacer.php
```

### 示例交互流程

#### 1. 站点选择
```
🔍 站点发现功能

请输入WordPress站点根目录路径（例如：/var/www/ 或 /home/<USER>/sites/）
或者直接按回车使用默认路径 [/var/www/]: 

✅ 发现以下WordPress站点:
  1. mysite
     路径: /var/www/mysite

请选择要处理的站点 (1-1): 1
```

#### 2. URL猜测与选择
```
🎯 智能URL猜测与选择

✅ 发现以下可能的旧URL:
  1. https://www.123.com
     来源: 数据库首页URL配置 (优先级: 10)

请选择旧URL (1-1): 1
请输入新URL: https://www.321.com
```

#### 3. 操作确认（新增多步骤显示）
```
📋 操作确认

站点信息:
  站点名称: mysite
  站点路径: /var/www/mysite

URL替换信息:
  第一步 - 完整URL替换:
    旧URL: https://www.123.com
    新URL: https://www.321.com
  第二步 - 域名替换:
    旧域名: www.123.com
    新域名: www.321.com

操作范围:
  ✓ 第一步：完整URL替换（数据库 + 文件）
  ✓ 第二步：域名替换（数据库 + 文件）

⚠️  确定要执行URL替换操作吗？此操作将修改数据库和文件！(y/N): y
```

#### 4. 执行过程（新增分步显示）
```
🚀 开始执行URL替换操作

🚀 第一步：执行完整URL替换
正在替换数据库中的完整URL...
数据库URL替换完毕！总数：1250，替换数：45，用时：2.35秒
正在替换文件中的完整URL...
文件URL替换完成: 处理 156 个文件，替换 12 个文件
✅ 第一步完成！

🚀 第二步：执行域名替换
正在替换数据库中的域名...
数据库URL替换完毕！总数：1250，替换数：23，用时：1.87秒
正在替换文件中的域名...
文件URL替换完成: 处理 156 个文件，替换 8 个文件
✅ 第二步完成！
```

#### 5. 统计信息（新增分步统计）
```
📊 多步骤替换统计信息:

🔸 第一步（完整URL替换）:
  数据库记录总数: 1250
  数据库替换记录数: 45
  处理文件总数: 156
  替换文件数: 12

🔸 第二步（域名替换）:
  数据库记录总数: 1250
  数据库替换记录数: 23
  处理文件总数: 156
  替换文件数: 8

🔸 总计:
  数据库记录总数: 2500
  数据库替换记录数: 68
  处理文件总数: 312
  替换文件数: 20
  总用时: 4.22秒

🎉 多步骤URL替换操作完成！
```

#### 6. 操作历史（新增多步骤记录）
```
📝 操作历史:

操作 #1:
  时间: 2025-01-17 15:30:45
  站点: mysite
  类型: 多步骤URL替换
  第一步 - 完整URL: https://www.123.com → https://www.321.com
  第二步 - 域名: www.123.com → www.321.com
  状态: completed
  备份: ./backups/mysite_backup_2025-01-17_15-30-25.sql
  用时: 4.22秒
```

## 🔄 不同场景的处理

### 场景1：域名相同（跳过第二步）
```
输入：
  旧URL: https://www.example.com/old-path
  新URL: https://www.example.com/new-path

显示：
  第一步 - 完整URL替换:
    旧URL: https://www.example.com/old-path
    新URL: https://www.example.com/new-path
  ⏭️  第二步：域名替换（跳过，域名相同）

执行：
  🚀 第一步：执行完整URL替换
  ⏭️  跳过第二步：域名相同或无效
```

### 场景2：子域名变更
```
输入：
  旧URL: https://old.example.com
  新URL: https://new.example.com

执行：
  第一步：https://old.example.com → https://new.example.com
  第二步：old.example.com → new.example.com
```

### 场景3：协议变更
```
输入：
  旧URL: http://www.example.com
  新URL: https://www.example.com

显示：
  ⏭️  第二步：域名替换（跳过，域名相同）

执行：
  只执行第一步，因为域名相同
```

## 💡 优势说明

### 为什么需要两步替换？

1. **完整URL引用**：
   ```html
   <a href="https://www.123.com/page">链接</a>
   <img src="https://www.123.com/image.jpg">
   ```

2. **域名引用**（第一步可能遗漏）：
   ```javascript
   var domain = 'www.123.com';
   var api_url = 'api.' + 'www.123.com';
   ```

3. **配置文件引用**：
   ```php
   define('DOMAIN', 'www.123.com');
   $allowed_hosts = ['www.123.com'];
   ```

### 替换覆盖率对比

| 替换方式 | 完整URL | 域名引用 | 覆盖率 |
|----------|---------|----------|--------|
| 单步替换 | ✅ | ❌ | ~80% |
| 多步替换 | ✅ | ✅ | ~95% |

## 🛡️ 安全特性

- **智能判断**：只有域名不同时才执行第二步
- **完整备份**：涵盖两步操作的完整备份
- **分步回滚**：可以基于备份回滚整个多步操作
- **详细日志**：记录每一步的详细执行信息

## 📋 注意事项

1. **备份重要性**：多步操作修改更多内容，强烈建议创建备份
2. **测试环境验证**：在生产环境使用前先在测试环境验证
3. **域名检查**：确保输入的新域名是正确的
4. **时间预估**：多步操作需要更多时间，请耐心等待

这个多步骤URL替换功能确保了更彻底、更可靠的WordPress站点URL迁移！🎉
