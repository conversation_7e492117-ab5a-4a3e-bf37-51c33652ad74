<?php
/**
 * 域名提取逻辑测试脚本
 * 验证新的extractDomain()方法是否正确处理各种URL格式
 */

echo "=== 域名提取逻辑测试 ===\n\n";

// 临时修改脚本以避免执行主程序
$content = file_get_contents('interactive_url_replacer.php');
$test_content = str_replace('$replacer->run();', '// $replacer->run(); // 测试模式', $content);
file_put_contents('temp_domain_test.php', $test_content);

try {
    include 'temp_domain_test.php';
    
    // 创建测试实例
    $replacer = new InteractiveURLReplacer();
    
    echo "✅ 脚本加载成功\n\n";
    
    // 获取extractDomain方法
    $reflection = new ReflectionClass($replacer);
    $extract_domain_method = $reflection->getMethod('extractDomain');
    $extract_domain_method->setAccessible(true);
    
    // 测试用例
    $test_cases = array(
        // 基本域名测试
        array(
            'url' => 'https://demo66.yhct.site',
            'expected' => 'demo66.yhct.site',
            'description' => '基本域名（无路径）'
        ),
        array(
            'url' => 'https://preview.yhct.site/zhenglingzhineng',
            'expected' => 'preview.yhct.site/zhenglingzhineng',
            'description' => '域名 + 路径'
        ),
        
        // 端口测试
        array(
            'url' => 'http://localhost:8080',
            'expected' => 'localhost:8080',
            'description' => '域名 + 端口'
        ),
        array(
            'url' => 'https://example.com:9090/api/v1',
            'expected' => 'example.com:9090/api/v1',
            'description' => '域名 + 端口 + 路径'
        ),
        
        // 复杂路径测试
        array(
            'url' => 'https://api.example.com/v1/users/123',
            'expected' => 'api.example.com/v1/users/123',
            'description' => '子域名 + 复杂路径'
        ),
        array(
            'url' => 'http://www.example.com/path/to/resource/',
            'expected' => 'www.example.com/path/to/resource/',
            'description' => '域名 + 多级路径（末尾斜杠）'
        ),
        
        // 根路径测试
        array(
            'url' => 'https://example.com/',
            'expected' => 'example.com',
            'description' => '域名 + 根路径（应忽略）'
        ),
        
        // 查询参数测试（应忽略）
        array(
            'url' => 'https://example.com/search?q=test&page=1',
            'expected' => 'example.com/search',
            'description' => '域名 + 路径 + 查询参数（应忽略查询参数）'
        ),
        
        // 锚点测试（应忽略）
        array(
            'url' => 'https://example.com/page#section1',
            'expected' => 'example.com/page',
            'description' => '域名 + 路径 + 锚点（应忽略锚点）'
        ),
        
        // 边界情况
        array(
            'url' => 'ftp://files.example.com/downloads',
            'expected' => 'files.example.com/downloads',
            'description' => 'FTP协议 + 路径'
        ),
        array(
            'url' => 'invalid-url',
            'expected' => null,
            'description' => '无效URL'
        ),
        array(
            'url' => '',
            'expected' => null,
            'description' => '空URL'
        )
    );
    
    echo "🔍 测试域名提取功能:\n\n";
    
    $passed = 0;
    $total = count($test_cases);
    
    foreach ($test_cases as $i => $case) {
        $result = $extract_domain_method->invoke($replacer, $case['url']);
        $success = ($result === $case['expected']);
        
        if ($success) {
            echo "✅ 测试 " . ($i + 1) . ": " . $case['description'] . "\n";
            echo "   URL: " . $case['url'] . "\n";
            echo "   结果: " . ($result ?: 'null') . "\n";
            $passed++;
        } else {
            echo "❌ 测试 " . ($i + 1) . ": " . $case['description'] . "\n";
            echo "   URL: " . $case['url'] . "\n";
            echo "   期望: " . ($case['expected'] ?: 'null') . "\n";
            echo "   实际: " . ($result ?: 'null') . "\n";
        }
        echo "\n";
    }
    
    echo "📊 测试结果: {$passed}/{$total} 通过\n\n";
    
    // 测试实际使用场景
    echo "🎯 实际使用场景测试:\n\n";
    
    $scenarios = array(
        array(
            'old_url' => 'https://demo66.yhct.site',
            'new_url' => 'https://preview.yhct.site/zhenglingzhineng',
            'description' => '用户报告的场景'
        ),
        array(
            'old_url' => 'http://localhost:8080/mysite',
            'new_url' => 'https://production.com/app',
            'description' => '开发到生产环境'
        ),
        array(
            'old_url' => 'https://old.example.com',
            'new_url' => 'https://new.example.com',
            'description' => '纯域名替换'
        ),
        array(
            'old_url' => 'https://staging.app.com/v1',
            'new_url' => 'https://api.app.com/v2',
            'description' => '子域名 + 版本路径变更'
        )
    );
    
    foreach ($scenarios as $i => $scenario) {
        echo "场景 " . ($i + 1) . ": " . $scenario['description'] . "\n";
        
        $old_domain = $extract_domain_method->invoke($replacer, $scenario['old_url']);
        $new_domain = $extract_domain_method->invoke($replacer, $scenario['new_url']);
        
        echo "  完整URL替换:\n";
        echo "    " . $scenario['old_url'] . " → " . $scenario['new_url'] . "\n";
        
        if ($old_domain && $new_domain && $old_domain !== $new_domain) {
            echo "  域名替换:\n";
            echo "    " . $old_domain . " → " . $new_domain . "\n";
            echo "  ✅ 将执行第二步替换\n";
        } else {
            echo "  ⏭️  跳过第二步（域名相同或无效）\n";
        }
        echo "\n";
    }
    
    // 向后兼容性测试
    echo "🔄 向后兼容性测试:\n\n";
    
    $compatibility_tests = array(
        array(
            'old_url' => 'https://www.example.com',
            'new_url' => 'https://www.newdomain.com',
            'expected_behavior' => '正常执行两步替换'
        ),
        array(
            'old_url' => 'http://example.com',
            'new_url' => 'https://example.com',
            'expected_behavior' => '只执行第一步（域名相同）'
        ),
        array(
            'old_url' => 'https://sub1.example.com',
            'new_url' => 'https://sub2.example.com',
            'expected_behavior' => '正常执行两步替换（子域名不同）'
        )
    );
    
    foreach ($compatibility_tests as $i => $test) {
        echo "兼容性测试 " . ($i + 1) . ":\n";
        echo "  " . $test['old_url'] . " → " . $test['new_url'] . "\n";
        
        $old_domain = $extract_domain_method->invoke($replacer, $test['old_url']);
        $new_domain = $extract_domain_method->invoke($replacer, $test['new_url']);
        
        $will_execute_step2 = ($old_domain && $new_domain && $old_domain !== $new_domain);
        
        echo "  期望行为: " . $test['expected_behavior'] . "\n";
        echo "  实际行为: " . ($will_execute_step2 ? '执行两步替换' : '只执行第一步') . "\n";
        echo "  域名提取: " . $old_domain . " → " . $new_domain . "\n";
        echo "\n";
    }
    
    // 总结
    if ($passed === $total) {
        echo "🎉 所有测试通过！新的域名提取逻辑工作正常！\n";
        echo "\n✨ 新功能特点:\n";
        echo "  ✅ 支持包含路径的域名提取\n";
        echo "  ✅ 支持端口号处理\n";
        echo "  ✅ 正确忽略查询参数和锚点\n";
        echo "  ✅ 保持向后兼容性\n";
        echo "  ✅ 处理各种边界情况\n";
    } else {
        echo "⚠️  部分测试失败，需要进一步调整\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('temp_domain_test.php')) {
        unlink('temp_domain_test.php');
    }
}

echo "\n=== 测试完成 ===\n";
