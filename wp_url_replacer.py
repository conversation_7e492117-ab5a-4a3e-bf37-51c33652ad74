#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress URL 替换工具
支持数据库URL替换和静态文件URL替换
作者: Assistant
版本: 1.0
"""

import os
import sys
import re
import json
import mysql.connector
from mysql.connector import Error
import configparser
from urllib.parse import urlparse
import argparse
from pathlib import Path
import logging
from typing import List, Dict, Tuple, Optional
import time

class WordPressURLReplacer:
    def __init__(self, site_path: str):
        """
        初始化WordPress URL替换器
        
        Args:
            site_path: WordPress站点根目录路径
        """
        self.site_path = Path(site_path)
        self.wp_config_path = self.site_path / 'wp-config.php'
        self.db_config = {}
        self.stats = {
            'db_total': 0,
            'db_replaced': 0,
            'files_processed': 0,
            'files_replaced': 0,
            'start_time': time.time()
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wp_url_replacer.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def parse_wp_config(self) -> bool:
        """
        解析wp-config.php文件获取数据库配置
        
        Returns:
            bool: 解析成功返回True，失败返回False
        """
        if not self.wp_config_path.exists():
            self.logger.error(f"wp-config.php 文件不存在: {self.wp_config_path}")
            return False
            
        try:
            with open(self.wp_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取数据库配置
            patterns = {
                'DB_NAME': r"define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                'DB_USER': r"define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                'DB_PASSWORD': r"define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]*)['\"]",
                'DB_HOST': r"define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                'DB_CHARSET': r"define\s*\(\s*['\"]DB_CHARSET['\"]\s*,\s*['\"]([^'\"]+)['\"]"
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    self.db_config[key] = match.group(1)
                else:
                    # 设置默认值
                    if key == 'DB_HOST':
                        self.db_config[key] = 'localhost'
                    elif key == 'DB_CHARSET':
                        self.db_config[key] = 'utf8mb4'
                    else:
                        self.logger.error(f"无法找到数据库配置: {key}")
                        return False
                        
            # 提取表前缀
            prefix_pattern = r"\$table_prefix\s*=\s*['\"]([^'\"]*)['\"]"
            match = re.search(prefix_pattern, content)
            self.db_config['table_prefix'] = match.group(1) if match else 'wp_'
            
            self.logger.info("成功解析wp-config.php配置")
            return True
            
        except Exception as e:
            self.logger.error(f"解析wp-config.php失败: {e}")
            return False
    
    def guess_old_urls(self) -> List[str]:
        """
        智能猜测可能的旧URL
        
        Returns:
            List[str]: 可能的旧URL列表
        """
        urls = []
        
        try:
            # 1. 从数据库获取siteurl和home选项
            connection = mysql.connector.connect(
                host=self.db_config['DB_HOST'],
                database=self.db_config['DB_NAME'],
                user=self.db_config['DB_USER'],
                password=self.db_config['DB_PASSWORD'],
                charset=self.db_config.get('DB_CHARSET', 'utf8mb4'),
                ssl_disabled=True,
                autocommit=True
            )
            
            cursor = connection.cursor()
            table_prefix = self.db_config['table_prefix']
            
            # 查询WordPress配置的URL
            query = f"""
            SELECT option_name, option_value 
            FROM {table_prefix}options 
            WHERE option_name IN ('siteurl', 'home')
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            for option_name, option_value in results:
                if option_value and option_value.startswith('http'):
                    urls.append(option_value.rstrip('/'))
            
            cursor.close()
            connection.close()
            
        except Error as e:
            self.logger.warning(f"从数据库获取URL失败: {e}")
        
        # 2. 基于站点目录名猜测URL
        site_name = self.site_path.name
        if site_name and site_name != '.':
            # 常见的本地开发环境URL模式
            local_patterns = [
                f"http://localhost/{site_name}",
                f"https://localhost/{site_name}",
                f"http://127.0.0.1/{site_name}",
                f"http://{site_name}.local",
                f"https://{site_name}.local",
                f"http://{site_name}.test",
                f"https://{site_name}.test",
                f"http://{site_name}.dev",
                f"https://{site_name}.dev"
            ]
            urls.extend(local_patterns)
        
        # 3. 从.htaccess文件中提取可能的URL
        htaccess_path = self.site_path / '.htaccess'
        if htaccess_path.exists():
            try:
                with open(htaccess_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 查找RewriteBase或其他URL相关配置
                    url_matches = re.findall(r'https?://[^\s\'"<>]+', content)
                    for url in url_matches:
                        urls.append(url.rstrip('/'))
            except Exception as e:
                self.logger.warning(f"读取.htaccess失败: {e}")
        
        # 去重并排序
        urls = list(set(urls))
        urls.sort()
        
        self.logger.info(f"猜测到 {len(urls)} 个可能的旧URL")
        return urls
    
    def replace_database_urls(self, old_url: str, new_url: str) -> Dict[str, int]:
        """
        替换数据库中的URL
        
        Args:
            old_url: 旧URL
            new_url: 新URL
            
        Returns:
            Dict[str, int]: 替换统计信息
        """
        stats = {'total': 0, 'replaced': 0}
        
        try:
            connection = mysql.connector.connect(
                host=self.db_config['DB_HOST'],
                database=self.db_config['DB_NAME'],
                user=self.db_config['DB_USER'],
                password=self.db_config['DB_PASSWORD'],
                charset=self.db_config.get('DB_CHARSET', 'utf8mb4'),
                ssl_disabled=True,
                autocommit=True
            )
            
            cursor = connection.cursor()
            table_prefix = self.db_config['table_prefix']
            
            # 获取所有表
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            for table_name in tables:
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                # 只处理文本类型的字段
                text_columns = []
                for column in columns:
                    col_name, col_type = column[0], column[1]
                    if any(t in col_type.lower() for t in ['char', 'text', 'blob', 'json']):
                        text_columns.append(col_name)
                
                if not text_columns:
                    continue
                
                # 分页查询数据
                page_size = 1000
                offset = 0
                
                while True:
                    # 查询数据
                    select_query = f"SELECT * FROM {table_name} LIMIT {page_size} OFFSET {offset}"
                    cursor.execute(select_query)
                    rows = cursor.fetchall()
                    
                    if not rows:
                        break
                    
                    # 获取列名
                    column_names = [desc[0] for desc in cursor.description]
                    
                    for row in rows:
                        stats['total'] += 1
                        row_dict = dict(zip(column_names, row))
                        updated_fields = {}
                        
                        # 检查每个文本字段
                        for col_name in text_columns:
                            if col_name in row_dict and row_dict[col_name]:
                                old_value = str(row_dict[col_name])
                                new_value = self._replace_url_in_content(old_value, old_url, new_url)
                                
                                if new_value != old_value:
                                    updated_fields[col_name] = new_value
                        
                        # 如果有字段需要更新
                        if updated_fields:
                            # 构建WHERE条件
                            where_conditions = []
                            where_values = []
                            
                            for col_name, value in row_dict.items():
                                if value is None:
                                    where_conditions.append(f"{col_name} IS NULL")
                                else:
                                    where_conditions.append(f"{col_name} = %s")
                                    where_values.append(value)
                            
                            # 构建UPDATE语句
                            set_clause = ", ".join([f"{col} = %s" for col in updated_fields.keys()])
                            where_clause = " AND ".join(where_conditions)
                            
                            update_query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause} LIMIT 1"
                            update_values = list(updated_fields.values()) + where_values
                            
                            cursor.execute(update_query, update_values)
                            if cursor.rowcount > 0:
                                stats['replaced'] += 1
                    
                    offset += page_size
            
            connection.commit()
            cursor.close()
            connection.close()
            
            self.logger.info(f"数据库URL替换完成: 总计 {stats['total']} 条记录，替换 {stats['replaced']} 条")
            
        except Error as e:
            self.logger.error(f"数据库URL替换失败: {e}")
            
        return stats

    def _replace_url_in_content(self, content: str, old_url: str, new_url: str) -> str:
        """
        在内容中替换URL，支持序列化数据和JSON数据

        Args:
            content: 原始内容
            old_url: 旧URL
            new_url: 新URL

        Returns:
            str: 替换后的内容
        """
        if not content or old_url not in content:
            return content

        # 1. 处理序列化数据
        if content.startswith('a:') and ';' in content:
            try:
                # 简单的序列化数据处理
                # 这里使用正则替换，因为Python没有PHP的序列化函数
                new_content = content.replace(old_url, new_url)

                # 更新序列化数据中的字符串长度
                def update_serialized_length(match):
                    length = match.group(1)
                    string_content = match.group(2)
                    actual_length = len(string_content.encode('utf-8'))
                    return f's:{actual_length}:"{string_content}";'

                new_content = re.sub(r's:(\d+):"([^"]*?)";', update_serialized_length, new_content)
                return new_content

            except Exception:
                # 如果序列化处理失败，使用普通替换
                return content.replace(old_url, new_url)

        # 2. 处理JSON数据
        elif content.strip().startswith(('{', '[')):
            try:
                # 尝试解析JSON
                data = json.loads(content)
                json_str = json.dumps(data)
                if old_url in json_str:
                    new_json_str = json_str.replace(old_url, new_url)
                    return new_json_str
                return content
            except json.JSONDecodeError:
                # 如果不是有效JSON，使用普通替换
                return content.replace(old_url, new_url)

        # 3. 普通字符串替换
        return content.replace(old_url, new_url)

    def replace_file_urls(self, old_url: str, new_url: str, file_extensions: List[str] = None) -> Dict[str, int]:
        """
        替换文件中的URL

        Args:
            old_url: 旧URL
            new_url: 新URL
            file_extensions: 要处理的文件扩展名列表

        Returns:
            Dict[str, int]: 替换统计信息
        """
        if file_extensions is None:
            file_extensions = ['.css', '.js', '.html', '.htm', '.php', '.json', '.xml']

        stats = {'files_processed': 0, 'files_replaced': 0}

        # 要排除的目录
        exclude_dirs = {
            'node_modules', '.git', '.svn', '__pycache__',
            'vendor', 'cache', 'logs', 'tmp', 'temp'
        }

        for root, dirs, files in os.walk(self.site_path):
            # 排除特定目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]

            for file in files:
                file_path = Path(root) / file

                # 检查文件扩展名
                if file_path.suffix.lower() not in file_extensions:
                    continue

                # 跳过过大的文件（超过10MB）
                try:
                    if file_path.stat().st_size > 10 * 1024 * 1024:
                        continue
                except OSError:
                    continue

                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    stats['files_processed'] += 1

                    # 检查是否包含旧URL
                    if old_url in content:
                        new_content = content.replace(old_url, new_url)

                        # 写回文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)

                        stats['files_replaced'] += 1
                        self.logger.info(f"已替换文件: {file_path}")

                except Exception as e:
                    self.logger.warning(f"处理文件失败 {file_path}: {e}")

        self.logger.info(f"文件URL替换完成: 处理 {stats['files_processed']} 个文件，替换 {stats['files_replaced']} 个文件")
        return stats

    def interactive_mode(self):
        """
        交互式模式
        """
        print("=== WordPress URL 替换工具 ===")
        print(f"站点路径: {self.site_path}")

        # 解析配置
        if not self.parse_wp_config():
            print("错误: 无法解析wp-config.php配置")
            return

        # 猜测旧URL
        print("\n正在猜测可能的旧URL...")
        old_urls = self.guess_old_urls()

        if old_urls:
            print("\n发现以下可能的旧URL:")
            for i, url in enumerate(old_urls, 1):
                print(f"{i}. {url}")

            while True:
                try:
                    choice = input(f"\n请选择旧URL (1-{len(old_urls)}) 或输入自定义URL: ").strip()

                    if choice.isdigit() and 1 <= int(choice) <= len(old_urls):
                        old_url = old_urls[int(choice) - 1]
                        break
                    elif choice.startswith('http'):
                        old_url = choice.rstrip('/')
                        break
                    else:
                        print("无效输入，请重试")
                except KeyboardInterrupt:
                    print("\n操作已取消")
                    return
        else:
            old_url = input("请输入旧URL: ").strip().rstrip('/')

        # 输入新URL
        new_url = input("请输入新URL: ").strip().rstrip('/')

        if not old_url or not new_url:
            print("错误: URL不能为空")
            return

        if old_url == new_url:
            print("错误: 新旧URL相同")
            return

        print(f"\n将要执行以下替换:")
        print(f"旧URL: {old_url}")
        print(f"新URL: {new_url}")

        # 确认操作
        confirm = input("\n确定要执行替换操作吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return

        # 执行替换
        print("\n开始执行URL替换...")

        # 替换数据库
        print("正在替换数据库中的URL...")
        db_stats = self.replace_database_urls(old_url, new_url)
        self.stats['db_total'] = db_stats['total']
        self.stats['db_replaced'] = db_stats['replaced']

        # 替换文件
        print("正在替换文件中的URL...")
        file_stats = self.replace_file_urls(old_url, new_url)
        self.stats['files_processed'] = file_stats['files_processed']
        self.stats['files_replaced'] = file_stats['files_replaced']

        # 显示统计信息
        self.show_stats()

    def show_stats(self):
        """
        显示统计信息
        """
        end_time = time.time()
        duration = end_time - self.stats['start_time']

        print("\n=== 替换完成 ===")
        print(f"数据库记录总数: {self.stats['db_total']}")
        print(f"数据库替换记录数: {self.stats['db_replaced']}")
        print(f"处理文件总数: {self.stats['files_processed']}")
        print(f"替换文件数: {self.stats['files_replaced']}")
        print(f"总用时: {duration:.2f} 秒")

        # 记录到日志
        self.logger.info(f"替换操作完成 - 数据库: {self.stats['db_replaced']}/{self.stats['db_total']}, "
                        f"文件: {self.stats['files_replaced']}/{self.stats['files_processed']}, "
                        f"用时: {duration:.2f}秒")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='WordPress URL 替换工具')
    parser.add_argument('site_path', help='WordPress站点根目录路径')
    parser.add_argument('--old-url', help='旧URL')
    parser.add_argument('--new-url', help='新URL')
    parser.add_argument('--files-only', action='store_true', help='只替换文件，不处理数据库')
    parser.add_argument('--db-only', action='store_true', help='只替换数据库，不处理文件')
    parser.add_argument('--extensions', help='要处理的文件扩展名，用逗号分隔 (默认: css,js,html,htm,php,json,xml)')

    args = parser.parse_args()

    # 检查站点路径
    site_path = Path(args.site_path)
    if not site_path.exists():
        print(f"错误: 站点路径不存在: {site_path}")
        sys.exit(1)

    if not (site_path / 'wp-config.php').exists():
        print(f"错误: 不是有效的WordPress站点 (缺少wp-config.php): {site_path}")
        sys.exit(1)

    # 创建替换器实例
    replacer = WordPressURLReplacer(str(site_path))

    # 命令行模式
    if args.old_url and args.new_url:
        old_url = args.old_url.rstrip('/')
        new_url = args.new_url.rstrip('/')

        if old_url == new_url:
            print("错误: 新旧URL相同")
            sys.exit(1)

        # 解析配置
        if not replacer.parse_wp_config():
            print("错误: 无法解析wp-config.php配置")
            sys.exit(1)

        print(f"开始URL替换: {old_url} -> {new_url}")

        # 执行替换
        if not args.files_only:
            print("正在替换数据库中的URL...")
            db_stats = replacer.replace_database_urls(old_url, new_url)
            replacer.stats['db_total'] = db_stats['total']
            replacer.stats['db_replaced'] = db_stats['replaced']

        if not args.db_only:
            print("正在替换文件中的URL...")
            extensions = None
            if args.extensions:
                extensions = ['.' + ext.strip() for ext in args.extensions.split(',')]

            file_stats = replacer.replace_file_urls(old_url, new_url, extensions)
            replacer.stats['files_processed'] = file_stats['files_processed']
            replacer.stats['files_replaced'] = file_stats['files_replaced']

        replacer.show_stats()

    else:
        # 交互式模式
        replacer.interactive_mode()


if __name__ == '__main__':
    main()
