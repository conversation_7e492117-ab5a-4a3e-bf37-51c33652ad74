# 统一交互式URL替换脚本 - 使用演示

## 快速开始

### 1. 启动脚本
```bash
php interactive_url_replacer.php
```

### 2. 输入体验说明
脚本支持两种输入模式：

**增强模式（推荐）**：
- ✅ 支持方向键（←→）移动光标
- ✅ 支持退格键编辑
- ✅ 支持输入历史记录（↑↓键）
- ✅ 支持Tab补全（部分功能）

**基础模式**：
- 如果系统不支持readline，会自动回退到基础输入
- 直接输入内容，按回车确认
- 不支持方向键编辑

### 3. 预期的交互流程

#### 欢迎界面
```
╔══════════════════════════════════════════════════════════════╗
║              统一的交互式WordPress URL替换脚本                ║
║                                                              ║
║  功能特性：                                                  ║
║  • 智能站点发现和选择                                       ║
║  • 精准URL猜测和选择                                        ║
║  • 操作确认和备份功能                                       ║
║  • 详细日志和回滚支持                                       ║
║  • 实时进度显示                                             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
```

#### 站点发现
```
🔍 站点发现功能

请输入WordPress站点根目录路径（例如：/var/www/ 或 /home/<USER>/sites/）
或者直接按回车使用默认路径 [/var/www/dev_sites/]: 

正在扫描WordPress站点...
扫描目录: /var/www/dev_sites/

✅ 发现以下WordPress站点:

  1. vincetest
     路径: /var/www/dev_sites/vincetest
  2. demoa
     路径: /var/www/dev_sites/demoa
  3. testsite
     路径: /var/www/dev_sites/testsite

总计: 3 个站点

请选择要处理的站点 (1-3): 1

✅ 已选择站点: vincetest
站点路径: /var/www/dev_sites/vincetest
```

#### URL猜测与选择
```
🎯 智能URL猜测与选择

正在分析站点并猜测可能的旧URL...

✅ 发现以下可能的旧URL:

  1. https://preview.yhct.site/vincetest
     来源: database_siteurl (优先级: 10)
  2. http://**************
     来源: server_ip (优先级: 8)
  3. http://localhost/vincetest
     来源: development (优先级: 9)
  4. https://localhost/vincetest
     来源: development (优先级: 8)
  5. http://vincetest.local
     来源: development (优先级: 7)

  6. 自定义输入URL

请选择旧URL (1-6): 1

请输入新URL: https://vincetest.com

✅ URL配置完成:
旧URL: https://preview.yhct.site/vincetest
新URL: https://vincetest.com
```

#### 操作确认
```
============================================================
📋 操作确认

站点信息:
  站点名称: vincetest
  站点路径: /var/www/dev_sites/vincetest

URL替换信息:
  旧URL: https://preview.yhct.site/vincetest
  新URL: https://vincetest.com

操作范围:
  ✓ 数据库URL替换
  ✓ 文件URL替换

============================================================

💾 数据备份选项
是否在操作前创建数据库备份？(Y/n): y

正在创建数据库备份...
✅ 数据库备份成功: ./backups/vincetest_backup_2025-01-17_14-30-25.sql

⚠️  确定要执行URL替换操作吗？此操作将修改数据库和文件！(y/N): y
```

#### 执行过程
```
🚀 开始执行URL替换操作

=== WordPress URL 替换工具 ===
站点路径: /var/www/dev_sites/vincetest
成功解析wp-config.php配置

开始执行URL替换...
正在替换数据库中的URL...
数据库URL替换完毕！总数：1250，替换数：45，用时：2.35秒

正在替换文件中的URL...
处理文件总数: 156
替换文件数: 12
总用时: 5.67秒

✅ URL替换操作完成！
```

#### 完成选项
```
============================================================
🎉 操作完成！

后续选项:
  1. 处理另一个站点
  2. 查看操作历史
  3. 回滚最近的操作
  4. 退出程序

请选择 (1-4): 2

📝 操作历史:

操作 #1:
  时间: 2025-01-17 14:30:45
  站点: vincetest
  旧URL: https://preview.yhct.site/vincetest
  新URL: https://vincetest.com
  状态: completed
  备份: ./backups/vincetest_backup_2025-01-17_14-30-25.sql
  用时: 5.67秒
```

## 主要特性演示

### 1. 智能站点发现
- 自动扫描指定目录
- 验证WordPress站点有效性
- 支持多目录扫描
- 清晰的站点列表显示

### 2. 精准URL猜测
- 基于数据库配置的URL提取
- 服务器IP地址检测
- 开发环境URL模式匹配
- 优先级排序显示

### 3. 安全操作流程
- 详细的操作预览
- 多重确认机制
- 自动数据库备份
- 完整的回滚支持

### 4. 用户友好界面
- 彩色输出提升可读性
- 清晰的步骤指引
- 实时进度反馈
- 错误处理和提示

### 5. 历史管理
- 完整的操作记录
- JSON格式数据存储
- 历史查看功能
- 基于备份的回滚

## 文件结构

执行后会创建以下文件和目录：

```
./
├── interactive_url_replacer.php    # 主脚本
├── backups/                        # 备份目录
│   └── vincetest_backup_2025-01-17_14-30-25.sql
├── operation_history.json          # 操作历史
├── interactive_replacer.log        # 详细日志
└── change.log                      # 更新日志
```

## 注意事项

1. **使用前备份**：虽然脚本提供自动备份，但建议手动备份重要数据
2. **测试环境验证**：在生产环境使用前，建议在测试环境中验证
3. **权限要求**：确保脚本有读写数据库和文件的权限
4. **MySQL工具**：回滚功能需要系统安装mysql和mysqldump命令
5. **PHP版本**：建议使用PHP 7.0或更高版本

## 故障排除

### 常见问题
1. **数据库连接失败**：检查wp-config.php配置和数据库服务状态
2. **备份失败**：检查mysqldump命令是否可用和数据库权限
3. **文件权限错误**：确保脚本对目标目录有读写权限
4. **URL格式错误**：确保输入的URL包含完整的协议（http://或https://）

### 日志查看
- 详细日志：`interactive_replacer.log`
- 操作历史：`operation_history.json`
- 错误信息会在控制台实时显示

这个统一的交互式脚本提供了完整、安全、用户友好的WordPress URL替换解决方案！
