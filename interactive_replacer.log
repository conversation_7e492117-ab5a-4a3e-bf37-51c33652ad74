2025-07-17 14:04:09 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:04:09 - 启动时间: 2025-07-17 14:04:09
2025-07-17 14:07:54 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:07:54 - 启动时间: 2025-07-17 14:07:54
2025-07-17 14:08:11 - 添加扫描目录: /var/www/dev_sites/
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-17 14:08:17 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-17 14:08:37 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:08:37 - 启动时间: 2025-07-17 14:08:37
2025-07-17 14:13:40 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:13:40 - 启动时间: 2025-07-17 14:13:40
2025-07-17 14:13:55 - 添加扫描目录: /var/www/dev_sites/
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-17 14:13:59 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-17 14:14:15 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:14:15 - 启动时间: 2025-07-17 14:14:15
2025-07-17 14:14:22 - 添加扫描目录: /var/www/
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/boyu
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_es
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_id
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_th
2025-07-17 14:14:24 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-17 14:14:28 - 选择站点: boyu (/var/www/boyu)
2025-07-17 14:20:53 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:20:53 - 启动时间: 2025-07-17 14:20:53
2025-07-17 14:21:24 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:21:24 - 启动时间: 2025-07-17 14:21:24
2025-07-17 14:21:34 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:21:34 - 启动时间: 2025-07-17 14:21:34
2025-07-17 14:22:19 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:22:19 - 启动时间: 2025-07-17 14:22:19
2025-07-17 14:58:19 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:58:19 - 启动时间: 2025-07-17 14:58:19
2025-07-17 14:59:46 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 14:59:46 - 启动时间: 2025-07-17 14:59:46
2025-07-17 15:01:51 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:01:51 - 启动时间: 2025-07-17 15:01:51
2025-07-17 15:01:51 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:01:51 - 启动时间: 2025-07-17 15:01:51
2025-07-17 15:01:51 - 成功解析wp-config.php配置
2025-07-17 15:01:52 - 猜测到 10 个可能的旧URL
2025-07-17 15:01:52 - 已替换文件: ./test_files/test.css
2025-07-17 15:02:45 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:02:45 - 启动时间: 2025-07-17 15:02:45
2025-07-17 15:04:19 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:04:19 - 启动时间: 2025-07-17 15:04:19
2025-07-17 15:04:25 - 添加扫描目录: /var/www/
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/boyu
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_es
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_id
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_th
2025-07-17 15:04:29 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-17 15:04:31 - 选择站点: boyu (/var/www/boyu)
2025-07-17 15:04:31 - 成功解析wp-config.php配置
2025-07-17 15:04:32 - 猜测到 10 个可能的旧URL
2025-07-17 15:15:01 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:15:01 - 启动时间: 2025-07-17 15:15:01
2025-07-17 15:15:01 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:15:01 - 启动时间: 2025-07-17 15:15:01
2025-07-17 15:15:01 - 成功解析wp-config.php配置
2025-07-17 15:22:05 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:22:05 - 启动时间: 2025-07-17 15:22:05
2025-07-17 15:22:07 - 添加扫描目录: /var/www/dev_sites/
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-17 15:22:10 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-17 15:22:15 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-17 15:22:15 - 启动时间: 2025-07-17 15:22:15
2025-07-17 15:22:19 - 添加扫描目录: /var/www/
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/boyu
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_es
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_id
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_th
2025-07-17 15:22:20 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-17 15:22:22 - 选择站点: boyu (/var/www/boyu)
2025-07-17 15:22:22 - 成功解析wp-config.php配置
2025-07-17 15:22:23 - 猜测到 10 个可能的旧URL
2025-07-18 10:33:58 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 10:33:58 - 启动时间: 2025-07-18 10:33:58
2025-07-18 10:33:58 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 10:33:58 - 启动时间: 2025-07-18 10:33:58
2025-07-18 10:38:23 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 10:38:23 - 启动时间: 2025-07-18 10:38:23
2025-07-18 10:38:25 - 添加扫描目录: /var/www/
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_es
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_id
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_th
2025-07-18 10:38:27 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-18 10:38:30 - 选择站点: yuchai_ar (/var/www/yuchai_ar)
2025-07-18 10:38:30 - 成功解析wp-config.php配置
2025-07-18 10:38:31 - 猜测到 10 个可能的旧URL
2025-07-18 10:38:38 - 选择旧URL: https://yuchaiar.yhct.top
2025-07-18 04:29:01 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 04:29:01 - 启动时间: 2025-07-18 04:29:01
2025-07-18 04:29:12 - 添加扫描目录: /var/www/vince_sites/
2025-07-18 04:29:14 - 发现WordPress站点: /var/www/vince_sites/sitemanager
2025-07-18 04:29:16 - 选择站点: sitemanager (/var/www/vince_sites/sitemanager)
2025-07-18 04:29:16 - 成功解析wp-config.php配置
2025-07-18 04:29:17 - 猜测到 10 个可能的旧URL
2025-07-18 04:29:21 - 选择旧URL: https://preview.yhct.site/sitemanager
2025-07-18 04:29:40 - 用户选择跳过数据库备份
2025-07-18 04:29:43 - 用户确认执行URL替换操作
2025-07-18 04:29:43 - 检查并修复数据库中的无效日期...
2025-07-18 04:29:43 - 未发现需要修复的无效日期
2025-07-18 04:29:43 - 数据库URL替换完毕！总数：9，替换数：9，用时：0.05秒
2025-07-18 04:29:44 - 文件URL替换完成: 处理 3031 个文件，替换 0 个文件
2025-07-18 04:29:44 - 检查并修复数据库中的无效日期...
2025-07-18 04:29:44 - 未发现需要修复的无效日期
2025-07-18 04:29:44 - 数据库URL替换完毕！总数：0，替换数：0，用时：0.02秒
2025-07-18 04:29:44 - 文件URL替换完成: 处理 3031 个文件，替换 0 个文件
2025-07-18 04:29:44 - 多步骤URL替换操作成功完成
2025-07-18 04:29:44 - 操作历史已保存
2025-07-18 06:17:32 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:17:32 - 启动时间: 2025-07-18 06:17:32
2025-07-18 06:17:42 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:17:45 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:17:57 - 选择站点: liandongyoushi (/var/www/dev_sites/liandongyoushi)
2025-07-18 06:17:57 - 成功解析wp-config.php配置
2025-07-18 06:17:58 - 猜测到 10 个可能的旧URL
2025-07-18 06:18:03 - 选择旧URL: https://demo67.yhct.site
2025-07-18 06:18:21 - 用户选择跳过数据库备份
2025-07-18 06:18:23 - 用户确认执行URL替换操作
2025-07-18 06:18:23 - 检查并修复数据库中的无效日期...
2025-07-18 06:18:23 - 未发现需要修复的无效日期
2025-07-18 06:18:23 - 数据库URL替换完毕！总数：491，替换数：491，用时：0.38秒
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-112.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-113.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-114.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-115.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-117.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-123.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-124.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-126.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-131.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-134.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-373.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-41.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-504.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-527.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-530.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-531.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-532.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-644.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-697.css
2025-07-18 06:18:24 - 文件URL替换完成: 处理 10827 个文件，替换 19 个文件
2025-07-18 06:18:24 - 检查并修复数据库中的无效日期...
2025-07-18 06:18:24 - 未发现需要修复的无效日期
2025-07-18 06:18:24 - 数据库URL替换完毕！总数：216，替换数：216，用时：0.55秒
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-381.css
2025-07-18 06:18:24 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-693.css
2025-07-18 06:18:25 - 文件URL替换完成: 处理 10827 个文件，替换 2 个文件
2025-07-18 06:18:25 - 多步骤URL替换操作成功完成
2025-07-18 06:18:25 - 操作历史已保存
2025-07-18 06:18:43 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:18:45 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:18:52 - 选择站点: zhenglingzhineng (/var/www/dev_sites/zhenglingzhineng)
2025-07-18 06:19:12 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:19:12 - 启动时间: 2025-07-18 06:19:12
2025-07-18 06:19:18 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:19:22 - 添加额外扫描目录: /
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:19:24 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:19:28 - 选择站点: zhenglingzhineng (/var/www/dev_sites/zhenglingzhineng)
2025-07-18 06:19:28 - 成功解析wp-config.php配置
2025-07-18 06:19:30 - 猜测到 10 个可能的旧URL
2025-07-18 06:19:33 - 选择旧URL: https://demo66.yhct.site
2025-07-18 06:21:26 - 用户选择跳过数据库备份
2025-07-18 06:21:29 - 用户确认执行URL替换操作
2025-07-18 06:21:29 - 检查并修复数据库中的无效日期...
2025-07-18 06:21:29 - 未发现需要修复的无效日期
2025-07-18 06:21:29 - 数据库URL替换完毕！总数：566，替换数：566，用时：0.47秒
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-201.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-37.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-583.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-586.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-587.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-588.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-703.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-89.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-95.css
2025-07-18 06:21:29 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-97.css
2025-07-18 06:21:29 - 文件URL替换完成: 处理 11274 个文件，替换 10 个文件
2025-07-18 06:21:29 - 检查并修复数据库中的无效日期...
2025-07-18 06:21:29 - 未发现需要修复的无效日期
2025-07-18 06:21:30 - 数据库URL替换完毕！总数：331，替换数：328，用时：0.71秒
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-100.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-101.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-102.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-109.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-159.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-183.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-256.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-578.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-584.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-589.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-88.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-98.css
2025-07-18 06:21:30 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-99.css
2025-07-18 06:21:30 - 文件URL替换完成: 处理 11274 个文件，替换 13 个文件
2025-07-18 06:21:30 - 多步骤URL替换操作成功完成
2025-07-18 06:21:30 - 操作历史已保存
2025-07-18 06:28:27 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:28:27 - 启动时间: 2025-07-18 06:28:27
2025-07-18 06:28:27 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:28:27 - 启动时间: 2025-07-18 06:28:27
2025-07-18 06:29:01 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:29:01 - 启动时间: 2025-07-18 06:29:01
2025-07-18 06:29:01 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:29:01 - 启动时间: 2025-07-18 06:29:01
2025-07-18 06:36:33 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:36:33 - 启动时间: 2025-07-18 06:36:33
2025-07-18 06:37:01 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:37:04 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:37:39 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:37:39 - 启动时间: 2025-07-18 06:37:39
2025-07-18 06:37:45 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:37:47 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:38:07 - 选择站点: liandongyoushi (/var/www/dev_sites/liandongyoushi)
2025-07-18 06:38:07 - 成功解析wp-config.php配置
2025-07-18 06:38:08 - 猜测到 10 个可能的旧URL
2025-07-18 06:38:24 - 选择旧URL: https://demo67.yhct.site
2025-07-18 06:38:56 - 用户选择跳过数据库备份
2025-07-18 06:38:59 - 用户确认执行URL替换操作
2025-07-18 06:38:59 - 检查并修复数据库中的无效日期...
2025-07-18 06:38:59 - 未发现需要修复的无效日期
2025-07-18 06:39:00 - 数据库URL替换完毕！总数：491，替换数：491，用时：0.35秒
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-112.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-113.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-114.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-115.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-117.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-123.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-124.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-126.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-131.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-134.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-373.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-41.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-504.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-527.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-530.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-531.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-532.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-644.css
2025-07-18 06:39:00 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-697.css
2025-07-18 06:39:00 - 文件URL替换完成: 处理 10827 个文件，替换 19 个文件
2025-07-18 06:39:00 - 检查并修复数据库中的无效日期...
2025-07-18 06:39:00 - 未发现需要修复的无效日期
2025-07-18 06:39:00 - 数据库URL替换完毕！总数：216，替换数：216，用时：0.56秒
2025-07-18 06:39:01 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-381.css
2025-07-18 06:39:01 - 已替换文件: /var/www/dev_sites/liandongyoushi/wp-content/uploads/elementor/css/post-693.css
2025-07-18 06:39:01 - 文件URL替换完成: 处理 10827 个文件，替换 2 个文件
2025-07-18 06:39:01 - 多步骤URL替换操作成功完成
2025-07-18 06:39:01 - 操作历史已保存
2025-07-18 06:39:15 - 添加扫描目录: /var/www/
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_es
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_id
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_th
2025-07-18 06:39:16 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-18 06:39:23 - 选择站点: zhenglingzhineng (/var/www/dev_sites/zhenglingzhineng)
2025-07-18 06:39:26 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 06:39:26 - 启动时间: 2025-07-18 06:39:26
2025-07-18 06:39:33 - 添加扫描目录: /var/www/dev_sites/
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/0f9f8a53
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/159ac6f2
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/20a13daf
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/256436ff
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/46cc4e01
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/4b352500
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/7e790f4c
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/Beauty-Equipment
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/boyu
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/c8df219a
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/cae29a74
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/d6cb8d6d
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/demoa
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/e6c6619f
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/f0ae603c
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/liandongyoushi
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/myelitestyle
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/seopeixun
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/shenghe
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/vincetest
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/weijiewu
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/yeehaitest
2025-07-18 06:39:35 - 发现WordPress站点: /var/www/dev_sites/zhenglingzhineng
2025-07-18 06:39:45 - 选择站点: zhenglingzhineng (/var/www/dev_sites/zhenglingzhineng)
2025-07-18 06:39:45 - 成功解析wp-config.php配置
2025-07-18 06:39:46 - 猜测到 10 个可能的旧URL
2025-07-18 06:39:48 - 选择旧URL: https://demo66.yhct.site
2025-07-18 06:40:08 - 用户选择跳过数据库备份
2025-07-18 06:40:09 - 用户确认执行URL替换操作
2025-07-18 06:40:09 - 检查并修复数据库中的无效日期...
2025-07-18 06:40:09 - 未发现需要修复的无效日期
2025-07-18 06:40:10 - 数据库URL替换完毕！总数：566，替换数：566，用时：0.51秒
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-201.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-37.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-583.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-586.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-587.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-588.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-703.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-89.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-95.css
2025-07-18 06:40:10 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-97.css
2025-07-18 06:40:10 - 文件URL替换完成: 处理 11274 个文件，替换 10 个文件
2025-07-18 06:40:10 - 检查并修复数据库中的无效日期...
2025-07-18 06:40:10 - 未发现需要修复的无效日期
2025-07-18 06:40:11 - 数据库URL替换完毕！总数：331，替换数：328，用时：0.73秒
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-100.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-101.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-102.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-109.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-159.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-183.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-256.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-578.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-584.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-589.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-88.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-98.css
2025-07-18 06:40:11 - 已替换文件: /var/www/dev_sites/zhenglingzhineng/wp-content/uploads/elementor/css/post-99.css
2025-07-18 06:40:11 - 文件URL替换完成: 处理 11274 个文件，替换 13 个文件
2025-07-18 06:40:11 - 多步骤URL替换操作成功完成
2025-07-18 06:40:11 - 操作历史已保存
2025-07-18 08:12:07 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 08:12:07 - 启动时间: 2025-07-18 08:12:07
2025-07-18 08:43:50 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 08:43:50 - 启动时间: 2025-07-18 08:43:50
2025-07-18 08:51:00 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 08:51:00 - 启动时间: 2025-07-18 08:51:00
2025-07-18 09:04:40 - === 统一的交互式WordPress URL替换脚本 ===
2025-07-18 09:04:40 - 启动时间: 2025-07-18 09:04:40
2025-07-18 09:04:41 - 添加扫描目录: /var/www/
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_ar
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_cn
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_es
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_fr
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_id
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_ru
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_th
2025-07-18 09:04:45 - 发现WordPress站点: /var/www/yuchai_vn
2025-07-18 09:04:49 - 选择站点: yuchai_ar (/var/www/yuchai_ar)
2025-07-18 09:04:49 - 成功解析wp-config.php配置
2025-07-18 09:04:50 - 猜测到 10 个可能的旧URL
