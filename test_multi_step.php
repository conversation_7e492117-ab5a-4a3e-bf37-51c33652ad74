<?php
/**
 * 多步骤URL替换功能测试脚本
 * 验证新的两步替换逻辑是否正常工作
 */

echo "=== 多步骤URL替换功能测试 ===\n\n";

// 临时修改脚本以避免执行主程序
$content = file_get_contents('interactive_url_replacer.php');
$test_content = str_replace('$replacer->run();', '// $replacer->run(); // 测试模式', $content);
file_put_contents('temp_multi_step_test.php', $test_content);

try {
    include 'temp_multi_step_test.php';
    
    // 创建测试实例
    $replacer = new InteractiveURLReplacer();
    
    echo "✅ 脚本加载成功\n\n";
    
    // 测试域名提取功能
    echo "🔍 测试1: 域名提取功能\n";
    
    $test_urls = array(
        'https://www.123.com' => 'www.123.com',
        'http://example.com/path' => 'example.com',
        'https://subdomain.example.com:8080/path?query=1' => 'subdomain.example.com',
        'ftp://files.example.com' => 'files.example.com',
        'invalid-url' => null
    );
    
    $reflection = new ReflectionClass($replacer);
    $extract_domain_method = $reflection->getMethod('extractDomain');
    $extract_domain_method->setAccessible(true);
    
    $domain_test_passed = 0;
    foreach ($test_urls as $url => $expected) {
        $result = $extract_domain_method->invoke($replacer, $url);
        if ($result === $expected) {
            echo "  ✅ {$url} → {$result}\n";
            $domain_test_passed++;
        } else {
            echo "  ❌ {$url} → {$result} (期望: {$expected})\n";
        }
    }
    
    echo "域名提取测试: {$domain_test_passed}/" . count($test_urls) . " 通过\n\n";
    
    // 测试统计信息结构
    echo "🔍 测试2: 统计信息结构\n";
    
    $stats_property = $reflection->getProperty('stats');
    $stats_property->setAccessible(true);
    $stats = $stats_property->getValue($replacer);
    
    $required_structure = array(
        'step1' => array('db_total', 'db_replaced', 'files_processed', 'files_replaced'),
        'step2' => array('db_total', 'db_replaced', 'files_processed', 'files_replaced'),
        'start_time'
    );
    
    $structure_test_passed = true;
    
    if (isset($stats['step1']) && isset($stats['step2']) && isset($stats['start_time'])) {
        echo "  ✅ 基本结构正确\n";
        
        foreach ($required_structure['step1'] as $key) {
            if (isset($stats['step1'][$key]) && isset($stats['step2'][$key])) {
                echo "  ✅ {$key} 字段存在\n";
            } else {
                echo "  ❌ {$key} 字段缺失\n";
                $structure_test_passed = false;
            }
        }
    } else {
        echo "  ❌ 统计信息结构不正确\n";
        $structure_test_passed = false;
    }
    
    echo "统计信息结构测试: " . ($structure_test_passed ? "通过" : "失败") . "\n\n";
    
    // 测试多步骤逻辑判断
    echo "🔍 测试3: 多步骤逻辑判断\n";
    
    $test_cases = array(
        array(
            'old_url' => 'https://www.123.com',
            'new_url' => 'https://www.321.com',
            'should_execute_step2' => true,
            'description' => '不同域名，应执行第二步'
        ),
        array(
            'old_url' => 'https://www.example.com',
            'new_url' => 'http://www.example.com',
            'should_execute_step2' => false,
            'description' => '相同域名，应跳过第二步'
        ),
        array(
            'old_url' => 'https://old.example.com/path',
            'new_url' => 'https://new.example.com/path',
            'should_execute_step2' => true,
            'description' => '不同子域名，应执行第二步'
        )
    );
    
    $logic_test_passed = 0;
    foreach ($test_cases as $case) {
        $old_domain = $extract_domain_method->invoke($replacer, $case['old_url']);
        $new_domain = $extract_domain_method->invoke($replacer, $case['new_url']);
        
        $will_execute_step2 = ($old_domain && $new_domain && $old_domain !== $new_domain);
        
        if ($will_execute_step2 === $case['should_execute_step2']) {
            echo "  ✅ {$case['description']}\n";
            echo "     {$case['old_url']} → {$case['new_url']}\n";
            echo "     域名: {$old_domain} → {$new_domain}\n";
            $logic_test_passed++;
        } else {
            echo "  ❌ {$case['description']}\n";
            echo "     期望: " . ($case['should_execute_step2'] ? '执行' : '跳过') . "第二步\n";
            echo "     实际: " . ($will_execute_step2 ? '执行' : '跳过') . "第二步\n";
        }
        echo "\n";
    }
    
    echo "多步骤逻辑测试: {$logic_test_passed}/" . count($test_cases) . " 通过\n\n";
    
    // 测试操作历史结构
    echo "🔍 测试4: 操作历史结构\n";
    
    // 模拟操作历史记录
    $mock_operation = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'site_name' => 'test_site',
        'site_path' => '/test/path',
        'old_url' => 'https://www.123.com',
        'new_url' => 'https://www.321.com',
        'old_domain' => 'www.123.com',
        'new_domain' => 'www.321.com',
        'backup_file' => null,
        'status' => 'completed',
        'multi_step' => true
    );
    
    $required_fields = array('old_domain', 'new_domain', 'multi_step');
    $history_test_passed = true;
    
    foreach ($required_fields as $field) {
        if (isset($mock_operation[$field])) {
            echo "  ✅ {$field} 字段存在\n";
        } else {
            echo "  ❌ {$field} 字段缺失\n";
            $history_test_passed = false;
        }
    }
    
    echo "操作历史结构测试: " . ($history_test_passed ? "通过" : "失败") . "\n\n";
    
    // 总体评估
    echo "📊 测试结果总结:\n";
    
    $total_tests = 4;
    $passed_tests = 0;
    
    if ($domain_test_passed === count($test_urls)) {
        echo "✅ 域名提取功能: 通过\n";
        $passed_tests++;
    } else {
        echo "❌ 域名提取功能: 失败\n";
    }
    
    if ($structure_test_passed) {
        echo "✅ 统计信息结构: 通过\n";
        $passed_tests++;
    } else {
        echo "❌ 统计信息结构: 失败\n";
    }
    
    if ($logic_test_passed === count($test_cases)) {
        echo "✅ 多步骤逻辑: 通过\n";
        $passed_tests++;
    } else {
        echo "❌ 多步骤逻辑: 失败\n";
    }
    
    if ($history_test_passed) {
        echo "✅ 操作历史结构: 通过\n";
        $passed_tests++;
    } else {
        echo "❌ 操作历史结构: 失败\n";
    }
    
    echo "\n🏆 总体得分: {$passed_tests}/{$total_tests}\n";
    
    if ($passed_tests === $total_tests) {
        echo "🎉 所有测试通过！多步骤URL替换功能已准备就绪！\n";
    } elseif ($passed_tests >= $total_tests * 0.75) {
        echo "👍 大部分测试通过，功能基本可用\n";
    } else {
        echo "⚠️  需要修复一些问题才能正常使用\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('temp_multi_step_test.php')) {
        unlink('temp_multi_step_test.php');
    }
}

echo "\n=== 测试完成 ===\n";
echo "\n💡 使用说明:\n";
echo "1. 输入完整URL（如 https://www.123.com 和 https://www.321.com）\n";
echo "2. 脚本会自动执行两步替换：\n";
echo "   - 第一步：完整URL替换\n";
echo "   - 第二步：域名替换（如果域名不同）\n";
echo "3. 查看详细的分步统计信息\n";
echo "4. 操作历史会记录多步骤替换的完整信息\n";
