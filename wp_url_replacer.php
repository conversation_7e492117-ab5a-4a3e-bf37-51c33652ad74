<?php
/**
 * WordPress URL 替换工具 - PHP版本
 * 基于原项目的DomainNameChanger类逻辑
 * 支持数据库和文件中的URL批量替换
 * 
 * 使用方法:
 * php wp_url_replacer.php /path/to/wordpress
 * php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com"
 * php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com" --files-only
 * php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com" --db-only
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内存限制
ini_set('memory_limit', '512M');
set_time_limit(0);

class WordPressURLReplacer {
    
    private $site_path;
    private $wp_config_path;
    private $db_config = array();
    private $stats = array(
        'db_total' => 0,
        'db_replaced' => 0,
        'files_processed' => 0,
        'files_replaced' => 0,
        'start_time' => 0
    );
    
    public function __construct($site_path) {
        $this->site_path = rtrim($site_path, '/');
        $this->wp_config_path = $this->site_path . '/wp-config.php';
        $this->stats['start_time'] = microtime(true);
        
        $this->log("=== WordPress URL 替换工具 ===");
        $this->log("站点路径: " . $this->site_path);
    }
    
    /**
     * 日志输出
     */
    private function log($message) {
        echo date('Y-m-d H:i:s') . " - " . $message . "\n";
    }
    
    /**
     * 解析wp-config.php文件获取数据库配置
     */
    public function parseWpConfig() {
        if (!file_exists($this->wp_config_path)) {
            $this->log("错误: wp-config.php 文件不存在: " . $this->wp_config_path);
            return false;
        }
        
        $content = file_get_contents($this->wp_config_path);
        
        // 提取数据库配置
        $patterns = array(
            'DB_NAME' => "/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
            'DB_USER' => "/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
            'DB_PASSWORD' => "/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]*)['\"]/" ,
            'DB_HOST' => "/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]/",
            'DB_CHARSET' => "/define\s*\(\s*['\"]DB_CHARSET['\"]\s*,\s*['\"]([^'\"]+)['\"]/"
        );
        
        foreach ($patterns as $key => $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $this->db_config[$key] = $matches[1];
            } else {
                // 设置默认值
                if ($key == 'DB_HOST') {
                    $this->db_config[$key] = 'localhost';
                } elseif ($key == 'DB_CHARSET') {
                    $this->db_config[$key] = 'utf8mb4';
                } else {
                    $this->log("错误: 无法找到数据库配置: " . $key);
                    return false;
                }
            }
        }
        
        // 提取表前缀
        if (preg_match("/\\\$table_prefix\s*=\s*['\"]([^'\"]*)['\"]/" , $content, $matches)) {
            $this->db_config['table_prefix'] = $matches[1];
        } else {
            $this->db_config['table_prefix'] = 'wp_';
        }
        
        $this->log("成功解析wp-config.php配置");
        return true;
    }

    /**
     * 获取数据库配置
     */
    public function getDbConfig() {
        return $this->db_config;
    }

    /**
     * 将技术性的来源名称转换为用户友好的描述
     */
    private function getSourceDescription($source) {
        $descriptions = array(
            'database_siteurl' => '数据库站点URL配置',
            'database_home' => '数据库首页URL配置',
            'server_ip' => '服务器IP地址',
            'development' => '本地开发环境',
            'test_subdomain' => '测试子域名',
            'environment' => '当前环境推断',
            'historical' => '历史URL记录',
            'unknown' => '未知来源'
        );

        return isset($descriptions[$source]) ? $descriptions[$source] : $source;
    }
    
    /**
     * 智能猜测可能的旧URL - 重新设计，更精准的判定逻辑
     */
    public function guessOldUrls() {
        $urls = array();

        // 1. 从数据库获取WordPress配置的URL（原项目核心逻辑）
        $db_urls = $this->getUrlsFromDatabase();
        $urls = array_merge($urls, $db_urls);

        // 2. 获取服务器IP并构建URL（原项目逻辑）
        $ip_urls = $this->getServerIpUrls();
        $urls = array_merge($urls, $ip_urls);

        // 3. 基于当前环境推断可能的旧URL（新增智能逻辑）
        $env_urls = $this->guessUrlsFromCurrentEnvironment();
        $urls = array_merge($urls, $env_urls);

        // 4. 基于站点目录结构推断开发环境URL（新增）
        $dev_urls = $this->guessRelevantDevelopmentUrls();
        $urls = array_merge($urls, $dev_urls);

        // 过滤和排序 - 按相关性排序
        $urls = $this->filterAndRankUrls($urls);

        $this->log("猜测到 " . count($urls) . " 个可能的旧URL");
        return $urls;
    }

    /**
     * 从数据库获取URL（原项目逻辑 + 改进）
     */
    private function getUrlsFromDatabase() {
        $urls = array();

        try {
            $mysqli = new mysqli(
                $this->db_config['DB_HOST'],
                $this->db_config['DB_USER'],
                $this->db_config['DB_PASSWORD'],
                $this->db_config['DB_NAME']
            );

            if (!$mysqli->connect_error) {
                $mysqli->set_charset($this->db_config['DB_CHARSET']);
                $table_prefix = $this->db_config['table_prefix'];

                // 1. 获取siteurl和home选项（原项目逻辑）
                $query = "SELECT option_name, option_value FROM {$table_prefix}options WHERE option_name IN ('siteurl', 'home')";
                $result = $mysqli->query($query);

                if ($result) {
                    while ($row = $result->fetch_assoc()) {
                        if (!empty($row['option_value']) && strpos($row['option_value'], 'http') === 0) {
                            $url = rtrim($row['option_value'], '/');
                            $urls[] = array('url' => $url, 'source' => 'database_' . $row['option_name'], 'priority' => 10);
                        }
                    }
                }

                // 2. 查找可能的历史URL（从序列化数据中）
                $this->findHistoricalUrls($mysqli, $table_prefix, $urls);

                $mysqli->close();
            }
        } catch (Exception $e) {
            $this->log("警告: 从数据库获取URL失败: " . $e->getMessage());
        }

        return $urls;
    }

    /**
     * 查找历史URL（从主题选项、插件设置等）
     */
    private function findHistoricalUrls($mysqli, $table_prefix, &$urls) {
        // 从主题选项中查找可能的旧URL
        $query = "SELECT option_value FROM {$table_prefix}options WHERE option_name LIKE 'theme_mods_%' AND option_value LIKE '%http%' LIMIT 5";
        $result = $mysqli->query($query);

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $this->extractRelevantUrls($row['option_value'], $urls, 'theme_options', 5);
            }
        }

        // 从一些常见插件的设置中查找
        $plugin_options = array('wp_google_maps_settings', 'woocommerce_store_address');
        foreach ($plugin_options as $option_name) {
            $query = "SELECT option_value FROM {$table_prefix}options WHERE option_name = '{$option_name}' AND option_value LIKE '%http%'";
            $result = $mysqli->query($query);

            if ($result && $row = $result->fetch_assoc()) {
                $this->extractRelevantUrls($row['option_value'], $urls, 'plugin_settings', 3);
            }
        }
    }

    /**
     * 获取服务器IP URL（原项目逻辑改进）
     */
    private function getServerIpUrls() {
        $urls = array();

        // 原项目的逻辑：通过外部服务获取IP
        $external_services = array(
            'https://ip.me/ip',
            'https://ipinfo.io/ip'
        );

        foreach ($external_services as $service) {
            try {
                $context = stream_context_create(array(
                    'http' => array(
                        'timeout' => 3,
                        'user_agent' => 'WordPress URL Replacer'
                    )
                ));

                $server_ip = @file_get_contents($service, false, $context);
                if ($server_ip && filter_var(trim($server_ip), FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                    $server_ip = trim($server_ip);
                    $urls[] = array('url' => "http://{$server_ip}", 'source' => 'server_ip', 'priority' => 8);
                    $urls[] = array('url' => "https://{$server_ip}", 'source' => 'server_ip', 'priority' => 7);
                    break;
                }
            } catch (Exception $e) {
                continue;
            }
        }

        return $urls;
    }

    /**
     * 基于当前环境推断可能的旧URL
     */
    private function guessUrlsFromCurrentEnvironment() {
        $urls = array();
        $site_name = basename($this->site_path);

        // 如果当前通过某个域名访问，推断可能的旧域名
        if (isset($_SERVER['HTTP_HOST'])) {
            $current_host = $_SERVER['HTTP_HOST'];

            // 如果当前是生产域名，推断可能的测试域名
            if (!in_array($current_host, array('localhost', '127.0.0.1'))) {
                $domain_parts = explode('.', $current_host);
                if (count($domain_parts) >= 2) {
                    $base_domain = $domain_parts[count($domain_parts) - 2] . '.' . $domain_parts[count($domain_parts) - 1];

                    // 常见的测试子域名
                    $test_subdomains = array('test', 'staging', 'dev', 'preview');
                    foreach ($test_subdomains as $subdomain) {
                        $urls[] = array('url' => "https://{$subdomain}.{$base_domain}", 'source' => 'test_subdomain', 'priority' => 6);
                        $urls[] = array('url' => "http://{$subdomain}.{$base_domain}", 'source' => 'test_subdomain', 'priority' => 5);
                    }
                }
            }
        }

        return $urls;
    }

    /**
     * 猜测相关的开发环境URL（只包含最可能的）
     */
    private function guessRelevantDevelopmentUrls() {
        $urls = array();
        $site_name = basename($this->site_path);

        if (empty($site_name) || $site_name == '.') {
            return $urls;
        }

        // 只包含最常见和最相关的开发环境模式
        $dev_patterns = array(
            // 最常见的本地开发模式
            array('url' => "http://localhost/{$site_name}", 'priority' => 9),
            array('url' => "https://localhost/{$site_name}", 'priority' => 8),

            // 本地域名模式
            array('url' => "http://{$site_name}.local", 'priority' => 7),
            array('url' => "https://{$site_name}.local", 'priority' => 6),
            array('url' => "http://{$site_name}.test", 'priority' => 6),

            // 常见端口
            array('url' => "http://localhost:8080/{$site_name}", 'priority' => 5),
            array('url' => "http://localhost:8000/{$site_name}", 'priority' => 5),
        );

        foreach ($dev_patterns as $pattern) {
            $urls[] = array('url' => $pattern['url'], 'source' => 'development', 'priority' => $pattern['priority']);
        }

        return $urls;
    }

    /**
     * 从字符串中提取相关的URL
     */
    private function extractRelevantUrls($content, &$urls, $source, $priority) {
        if (empty($content)) {
            return;
        }

        $site_name = basename($this->site_path);

        // 只提取包含站点名或看起来相关的URL
        if (preg_match_all('/https?:\/\/[^\s\'"<>]+/', $content, $matches)) {
            foreach ($matches[0] as $url) {
                $url = rtrim($url, '.,;:!?)]}"\'');

                // 过滤条件：URL必须看起来相关
                if ($this->isRelevantUrl($url, $site_name)) {
                    $urls[] = array('url' => rtrim($url, '/'), 'source' => $source, 'priority' => $priority);
                }
            }
        }
    }

    /**
     * 判断URL是否相关
     */
    private function isRelevantUrl($url, $site_name) {
        // 排除明显不相关的URL
        $irrelevant_patterns = array(
            'wordpress.org',
            'w3.org',
            'google.com',
            'googleapis.com',
            'facebook.com',
            'twitter.com',
            'api.',
            'cdn.',
            'gravatar.com'
        );

        foreach ($irrelevant_patterns as $pattern) {
            if (strpos($url, $pattern) !== false) {
                return false;
            }
        }

        // 如果URL包含站点名，认为相关
        if (!empty($site_name) && strpos($url, $site_name) !== false) {
            return true;
        }

        // 如果是本地开发环境URL，认为相关
        if (strpos($url, 'localhost') !== false ||
            strpos($url, '127.0.0.1') !== false ||
            preg_match('/\.(local|test|dev)/', $url)) {
            return true;
        }

        // 如果URL看起来像是项目相关的域名
        $parsed = parse_url($url);
        if (isset($parsed['host'])) {
            $host = $parsed['host'];
            // 简单的域名，可能是项目域名
            if (substr_count($host, '.') <= 2 && !preg_match('/\d+\.\d+\.\d+\.\d+/', $host)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 过滤和排序URL
     */
    private function filterAndRankUrls($url_data) {
        // 去重
        $unique_urls = array();
        foreach ($url_data as $item) {
            $url = $item['url'];
            if (!isset($unique_urls[$url])) {
                $unique_urls[$url] = $item;
            } else {
                // 如果已存在，保留优先级更高的
                if ($item['priority'] > $unique_urls[$url]['priority']) {
                    $unique_urls[$url] = $item;
                }
            }
        }

        // 按优先级排序
        uasort($unique_urls, function($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        // 返回包含完整信息的数组
        return array_values($unique_urls);
    }



    
    /**
     * 交互式模式
     */
    public function interactiveMode() {
        // 解析配置
        if (!$this->parseWpConfig()) {
            return false;
        }
        
        // 猜测旧URL
        echo "\n正在猜测可能的旧URL...\n";
        $old_urls = $this->guessOldUrls();
        
        if (!empty($old_urls)) {
            echo "\n发现以下可能的旧URL:\n";
            foreach ($old_urls as $i => $url_info) {
                $url = is_array($url_info) ? $url_info['url'] : $url_info;
                $source = is_array($url_info) ? $url_info['source'] : 'unknown';
                $priority = is_array($url_info) ? $url_info['priority'] : 5;

                $source_desc = $this->getSourceDescription($source);
                echo ($i + 1) . ". " . $url . " (来源: " . $source_desc . ", 优先级: " . $priority . ")\n";
            }

            while (true) {
                echo "\n请选择旧URL (1-" . count($old_urls) . ") 或输入自定义URL: ";
                $choice = trim(fgets(STDIN));

                if (is_numeric($choice) && $choice >= 1 && $choice <= count($old_urls)) {
                    $selected_url_info = $old_urls[$choice - 1];
                    $old_url = is_array($selected_url_info) ? $selected_url_info['url'] : $selected_url_info;
                    break;
                } elseif (strpos($choice, 'http') === 0) {
                    $old_url = rtrim($choice, '/');
                    break;
                } else {
                    echo "无效输入，请重试\n";
                }
            }
        } else {
            echo "请输入旧URL: ";
            $old_url = rtrim(trim(fgets(STDIN)), '/');
        }
        
        // 输入新URL
        echo "请输入新URL: ";
        $new_url = rtrim(trim(fgets(STDIN)), '/');
        
        if (empty($old_url) || empty($new_url)) {
            $this->log("错误: URL不能为空");
            return false;
        }
        
        if ($old_url == $new_url) {
            $this->log("错误: 新旧URL相同");
            return false;
        }
        
        echo "\n将要执行以下替换:\n";
        echo "旧URL: " . $old_url . "\n";
        echo "新URL: " . $new_url . "\n";
        
        // 确认操作
        echo "\n确定要执行替换操作吗？(y/N): ";
        $confirm = trim(fgets(STDIN));
        if (strtolower($confirm) !== 'y') {
            $this->log("操作已取消");
            return false;
        }
        
        // 执行替换
        return $this->executeReplace($old_url, $new_url, true, true);
    }
    
    /**
     * 执行URL替换
     */
    public function executeReplace($old_url, $new_url, $replace_db = true, $replace_files = true) {
        $this->log("\n开始执行URL替换...");
        
        // 替换数据库
        if ($replace_db) {
            $this->log("正在替换数据库中的URL...");
            $db_stats = $this->replaceDatabaseUrls($old_url, $new_url);
            $this->stats['db_total'] = $db_stats['total'];
            $this->stats['db_replaced'] = $db_stats['replace'];
        }
        
        // 替换文件
        if ($replace_files) {
            $this->log("正在替换文件中的URL...");
            $file_stats = $this->replaceFileUrls($old_url, $new_url);
            $this->stats['files_processed'] = $file_stats['files_processed'];
            $this->stats['files_replaced'] = $file_stats['files_replaced'];
        }
        
        // 显示统计信息
        $this->showStats();
        return true;
    }

    /**
     * 替换数据库中的URL - 基于原项目DomainNameChanger类的逻辑
     */
    public function replaceDatabaseUrls($old_url, $new_url) {
        $config = array(
            'change_from' => array($old_url),
            'change_to' => array($new_url),
            'host' => $this->db_config['DB_HOST'],
            'user' => $this->db_config['DB_USER'],
            'pw' => $this->db_config['DB_PASSWORD'],
            'db' => $this->db_config['DB_NAME'],
            'charset' => $this->db_config['DB_CHARSET'],
            'debug' => false,
        );

        $domain_name_changer = new DomainNameChanger($config);
        $status = $domain_name_changer->do_it();

        $this->log(sprintf(
            "数据库URL替换完毕！总数：%s，替换数：%s，用时：%.2f秒",
            $status['total'],
            $status['replace'],
            $status['time_used']
        ));

        return $status;
    }

    /**
     * 替换文件中的URL
     */
    public function replaceFileUrls($old_url, $new_url, $extensions = null) {
        if ($extensions === null) {
            $extensions = array('.css', '.js', '.html', '.htm', '.php', '.json', '.xml');
        }

        $stats = array('files_processed' => 0, 'files_replaced' => 0);

        // 要排除的目录
        $exclude_dirs = array(
            'node_modules', '.git', '.svn', '__pycache__',
            'vendor', 'cache', 'logs', 'tmp', 'temp', 'wp-content/cache'
        );

        $this->scanDirectory($this->site_path, $old_url, $new_url, $extensions, $exclude_dirs, $stats);

        $this->log(sprintf(
            "文件URL替换完成: 处理 %d 个文件，替换 %d 个文件",
            $stats['files_processed'],
            $stats['files_replaced']
        ));

        return $stats;
    }

    /**
     * 递归扫描目录
     */
    private function scanDirectory($dir, $old_url, $new_url, $extensions, $exclude_dirs, &$stats) {
        if (!is_dir($dir)) {
            return;
        }

        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            $path = $dir . '/' . $item;

            if (is_dir($path)) {
                // 检查是否为排除目录
                $should_exclude = false;
                foreach ($exclude_dirs as $exclude_dir) {
                    if (strpos($path, $exclude_dir) !== false) {
                        $should_exclude = true;
                        break;
                    }
                }

                if (!$should_exclude) {
                    $this->scanDirectory($path, $old_url, $new_url, $extensions, $exclude_dirs, $stats);
                }
            } elseif (is_file($path)) {
                $this->processFile($path, $old_url, $new_url, $extensions, $stats);
            }
        }
    }

    /**
     * 处理单个文件
     */
    private function processFile($file_path, $old_url, $new_url, $extensions, &$stats) {
        // 检查文件扩展名
        $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        if (!in_array('.' . $ext, $extensions)) {
            return;
        }

        // 跳过过大的文件（超过10MB）
        if (filesize($file_path) > 10 * 1024 * 1024) {
            return;
        }

        try {
            $content = file_get_contents($file_path);
            if ($content === false) {
                return;
            }

            $stats['files_processed']++;

            // 检查是否包含旧URL
            if (strpos($content, $old_url) !== false) {
                $new_content = str_replace($old_url, $new_url, $content);

                if (file_put_contents($file_path, $new_content) !== false) {
                    $stats['files_replaced']++;
                    $this->log("已替换文件: " . $file_path);
                }
            }
        } catch (Exception $e) {
            $this->log("处理文件失败 " . $file_path . ": " . $e->getMessage());
        }
    }

    /**
     * 显示统计信息
     */
    public function showStats() {
        $end_time = microtime(true);
        $duration = $end_time - $this->stats['start_time'];

        echo "\n=== 替换完成 ===\n";
        echo "数据库记录总数: " . $this->stats['db_total'] . "\n";
        echo "数据库替换记录数: " . $this->stats['db_replaced'] . "\n";
        echo "处理文件总数: " . $this->stats['files_processed'] . "\n";
        echo "替换文件数: " . $this->stats['files_replaced'] . "\n";
        echo "总用时: " . sprintf("%.2f", $duration) . " 秒\n";
    }
}

/************************** URL Replace Functions Start ***************************/

/**
 * 判断字符串是否JSON格式
 */
if (!function_exists('isJson')) {
    function isJson($string) {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}

/**
 * 递归反序列化
 */
if (!function_exists('digui_maybe_unserialize')) {
    function digui_maybe_unserialize($string, $tries = 0) {
        $unserialize_string = maybe_unserialize($string);
        ++$tries;
        if ($string == $unserialize_string) {
            return array('return' => $string, 'tries' => --$tries);
        } else {
            return digui_maybe_unserialize($unserialize_string, $tries);
        }
    }
}

/**
 * 递归序列化
 */
if (!function_exists('digui_maybe_serialize')) {
    function digui_maybe_serialize($string, $tries) {
        if ($tries > 0) {
            $string = maybe_serialize($string);
            if (--$tries > 0) {
                return digui_maybe_serialize($string, $tries);
            } else {
                return $string;
            }
        }
        return $string;
    }
}

/**
 * 递归替换
 */
if (!function_exists('digui_replace')) {
    function digui_replace($string, $search, $replace) {
        if (is_array($string)) {
            foreach ($string as &$item) {
                $item = digui_replace($item, $search, $replace);
            }
            return $string;
        } elseif (is_string($string)) {
            if (is_array($replace)) { //multi to multi
                foreach ((array)$search as $key => $one_search) {
                    $string = str_replace($one_search, $replace[$key], $string);
                }
            } else {
                foreach ((array)$search as $key => $one_search) {
                    $string = str_replace($one_search, $replace, $string);
                }
            }
            return $string;
        } else {
            return $string;
        }
    }
}

/**
 * WordPress的maybe_serialize函数
 */
if (!function_exists('maybe_serialize')) {
    function maybe_serialize($data) {
        if (is_array($data) || is_object($data)) {
            return serialize($data);
        }

        if (is_serialized($data, false)) {
            return $data;
        }

        return $data;
    }
}

/**
 * WordPress的maybe_unserialize函数
 */
if (!function_exists('maybe_unserialize')) {
    function maybe_unserialize($original) {
        if (is_serialized($original)) {
            return @unserialize($original);
        }
        return $original;
    }
}

/**
 * WordPress的is_serialized函数
 */
if (!function_exists('is_serialized')) {
    function is_serialized($data, $strict = true) {
        if (!is_string($data)) {
            return false;
        }
        $data = trim($data);
        if ('N;' == $data) {
            return true;
        }
        if (strlen($data) < 4) {
            return false;
        }
        if (':' !== $data[1]) {
            return false;
        }
        if ($strict) {
            $lastc = substr($data, -1);
            if (';' !== $lastc && '}' !== $lastc) {
                return false;
            }
        } else {
            $semicolon = strpos($data, ';');
            $brace     = strpos($data, '}');
            if (false === $semicolon && false === $brace) {
                return false;
            }
            if (false !== $semicolon && $semicolon < 3) {
                return false;
            }
            if (false !== $brace && $brace < 4) {
                return false;
            }
        }
        $token = $data[0];
        switch ($token) {
            case 's':
                if ($strict) {
                    if ('"' !== substr($data, -2, 1)) {
                        return false;
                    }
                } elseif (false === strpos($data, '"')) {
                    return false;
                }
            case 'a':
            case 'O':
                return (bool) preg_match("/^{$token}:[0-9]+:/s", $data);
            case 'b':
            case 'i':
            case 'd':
                $end = $strict ? '$' : '';
                return (bool) preg_match("/^{$token}:[0-9.E-]+;$end/", $data);
        }
        return false;
    }
}

/**
 * Domain Name Changer - 完全基于原项目的逻辑
 */
if (!class_exists('DomainNameChanger')) {
    class DomainNameChanger {
        protected $mysqli;
        protected $change_from = array();
        protected $change_to = array();
        protected $host, $user, $pw, $db, $charset = null;
        protected $tables = array();
        protected $one_row;
        protected $replace_sql;
        protected $ok = 0;
        protected $count = 0;
        protected $min_print = 1;
        protected $max_print = -1;
        protected $time_start = 0;
        protected $time_end = 0;
        protected $total_query_time = 0; //数据查询时间
        protected $total_update_time = 0; //数据更新时间
        protected $total_replace_time = 0; //数据替换时间
        protected $debug = false;

        protected $get_row_per_query = 10000;

        public function __construct($config) {
            $this->change_from = $config['change_from'];
            $this->change_to = $config['change_to'];
            $this->host = $config['host'];
            $this->user = $config['user'];
            $this->pw = $config['pw'];
            $this->db = $config['db'];
            $this->charset = $config['charset'];
            $this->debug = $config['debug'];

            $this->time_start = microtime(true);

            $this->connect_to_mysql();
        }

        public function connect_to_mysql() {
            try {
                $this->mysqli = new mysqli($this->host, $this->user, $this->pw, $this->db);
                $this->mysqli->set_charset($this->charset);
            } catch (Exception $e) {
                echo "Can't Connect To MYSQL , Error Message:" . $e;
            }
        }

        protected function get_all_table() {
            //get all tables
            $sql = sprintf("Show tables;");
            $result = $this->mysqli->query($sql);

            //all tables in DB
            while ($row = $result->fetch_assoc()) {
                $this->tables[$row['Tables_in_' . $this->db]] = '';
            }

            if ($this->debug) {
                var_dump($this->tables);
            }
        }

        protected function contruct_tables() {
            if ($this->tables) {
                //contruct table structure
                foreach ($this->tables as $table_name => $table_cols_name_type) {
                    $desc_table_sql = sprintf("DESC `%s`;", $table_name);
                    $result = $this->mysqli->query($desc_table_sql);
                    $cols = array();
                    while ($row = $result->fetch_assoc()) {
                        //if this field type is string, mark it 1 ,eles mark it 0
                        $cols[$row['Field']] = (preg_match("/char|blob|text|enum/i", $row['Type']) ? 1 : 0);
                    }
                    $this->tables[$table_name] = $cols;
                }
            }
            if ($this->debug) {
                var_dump($this->tables);
            }
        }

        //这个函数做的判断，也许可以改进。
        //现在所有的列，无论什么形式的字段都做判断
        protected function is_match_string($table_name) {
            if ($this->one_row) {
                foreach ($this->change_from as $search_string) {
                    foreach ($this->one_row as $key => $value) {
                        if ($this->tables[$table_name][$key]) {
                            //经过json格式化之后，普通的字符串，前后会加入双引号，所以要使用trim去除。
                            if (isJson($value) && stripos($value, trim(json_encode($search_string), '"')) !== false) {
                                return true;
                            } elseif (stripos($value, $search_string) !== false) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
        }

        public function serialized_string_replace($matches) {
            $str = $matches[2];
            if (is_array($this->change_to)) { //multi to multi
                foreach ($this->change_from as $key => $value) {
                    if (strpos($str, $value) !== false) { //替换域名长路径
                        $str = str_replace($value, $this->change_to[$key], $str);
                    } else { //其他不用替换
                    }
                }
            } else { //multi to single
                foreach ($this->change_from as $key => $value) {
                    if (strpos($str, $value) !== false) { //替换域名长路径
                        $str = str_replace($value, $this->change_to, $str);
                    } else { //其他不用替换
                    }
                }
            }
            return sprintf("s:%s:\"%s\";", strlen($str), $str);
        }

        function json_string_replace($string) {
            if (is_array($this->change_to)) { //multi to multi
                foreach ($this->change_from as $key => $value) {
                    //经过json格式化之后，普通的字符串，前后会加入双引号，所以要使用trim去除。
                    if (strpos($string, $value) !== false) {
                        $string = str_replace($value, $this->change_to[$key], $string);
                    } else {
                        $string = str_replace(trim(json_encode($value), '"'), trim(json_encode($this->change_to[$key]), '"'), $string);
                    }
                }
            } else { //multi to single
                foreach ($this->change_from as $key => $value) {
                    //经过json格式化之后，普通的字符串，前后会加入双引号，所以要使用trim去除。
                    if (strpos($string, $value) !== false) {
                        $string = str_replace($value, $this->change_to, $string);
                    } else {
                        $string = str_replace(trim(json_encode($value), '"'), trim(json_encode($this->change_to), '"'), $string);
                    }
                }
            }
            return $string;
        }

        function normal_string_replace($string) {
            if (is_array($this->change_to)) { //multi to multi
                foreach ($this->change_from as $key => $value) {
                    $string = str_replace($value, $this->change_to[$key], $string);
                }
            } else { //multi to single
                foreach ($this->change_from as $key => $value) {
                    $string = str_replace($value, $this->change_to, $string);
                }
            }

            return $string;
        }

        //替换字符串
        protected function get_replace_string_sql($table_name) {
            $set_sql = array();
            $where_sql = array();
            foreach ($this->one_row as $key => $value) {
                if (is_string($value) and is_serialized($value)) {
                    $unserialize_return = digui_maybe_unserialize($value);
                    $new_value = digui_replace($unserialize_return['return'], $this->change_from, $this->change_to);
                    $new_value = digui_maybe_serialize($new_value, $unserialize_return['tries']);

                    if ($new_value != $value) {
                        $set_sql[] = sprintf("`%s`='%s'", $key, $this->mysqli->real_escape_string($new_value));
                    }
                } elseif (is_string($value) and isJson($value)) {
                    $new_value = $this->json_string_replace($value);
                    if ($new_value != $value) {
                        $set_sql[] = sprintf("`%s`='%s'", $key, $this->mysqli->real_escape_string($new_value));
                    }
                } elseif (is_string($value)) {
                    $new_value = $this->normal_string_replace($value);
                    if ($new_value != $value) {
                        $set_sql[] = sprintf("`%s`='%s'", $key, $this->mysqli->real_escape_string($new_value));
                    }
                }

                if ($value === null) {
                    $where_sql[] = sprintf("`%s` is null ", $key, $key);
                } else {
                    $where_sql[] = sprintf("`%s`='%s'", $key, $this->mysqli->real_escape_string($value));
                }
            }
            if (sizeof($set_sql) >= 1) {
                $this->replace_sql = sprintf("UPDATE `%s` SET %s WHERE %s LIMIT 1;", $table_name, implode(' , ', $set_sql), implode(' and ', $where_sql));
            } else {
                $this->replace_sql = '';
            }
        }

        //do change domain name
        public function change_domain_name() {
            if ($this->tables) {
                //find and replace contents in each table cols
                foreach ($this->tables as $table_name => $table_cols_name_type) {
                    if (sizeof(array_filter($table_cols_name_type)) >= 1) {
                        $each_table_query_run = 0;

                        $query_time_start = microtime(1);
                        $select_total_sql = sprintf("SELECT COUNT(*) AS total FROM `%s`;", $table_name);
                        $result_total = $this->mysqli->query($select_total_sql);
                        $row = $result_total->fetch_assoc();
                        $total = $row['total'];

                        $query_time_end = microtime(1);
                        $each_table_query_run += ($query_time_end - $query_time_start);

                        $page = 1;

                        while (1) {
                            $offset = $this->get_row_per_query * ($page - 1);

                            $query_time_start = microtime(1);
                            $select_all_col_sql = sprintf("SELECT `%s` FROM `%s` LIMIT %d,%d;", implode('`,`', array_keys($table_cols_name_type)), $table_name, $offset, $this->get_row_per_query);

                            $result = $this->mysqli->query($select_all_col_sql);
                            $current_get = $result->num_rows;

                            $query_time_end = microtime(1);
                            $each_table_query_run += ($query_time_end - $query_time_start);

                            while ($this->one_row = $result->fetch_assoc()) {
                                if ($this->is_match_string($table_name)) {
                                    $replace_time_start = microtime(1);
                                    $this->get_replace_string_sql($table_name);
                                    $replace_time_end = microtime(1);
                                    $this->total_replace_time += $replace_time_end - $replace_time_start;
                                    if ($this->replace_sql) {
                                        $update_time_start = microtime(1);
                                        $update_result = $this->mysqli->query($this->replace_sql);
                                        $update_time_end = microtime(1);
                                        $this->total_update_time += $update_time_end - $update_time_start;

                                        if ($update_result && $this->mysqli->affected_rows > 0) {
                                            $this->ok++;
                                        }
                                    }

                                    //输出一部分，用于调试
                                    $this->count++;
                                    if (($this->count >= $this->min_print && $this->count <= $this->max_print) || $this->max_print === -1) {
                                        continue;
                                    } else {
                                        break 2;
                                    }
                                }
                            }

                            if ($total <= (($page - 1) * $this->get_row_per_query + $current_get)) {
                                break;
                            }

                            $page++;
                        }

                        $this->total_query_time += $each_table_query_run;
                    }
                }
            }
        }

        public function do_it() {
            $this->get_all_table();
            $this->contruct_tables();
            $this->change_domain_name();
            return $this->get_status();
        }

        public function print_status() {
            $this->time_end = microtime(true);
            printf("Total:< %s > , Replace: < %s > , Time Use: < %s >\n", $this->count, $this->ok, ($this->time_end - $this->time_start));
        }

        public function get_status() {
            $this->time_end = microtime(true);
            return array(
                'total' => $this->count,
                'replace' => $this->ok,
                'query_time' => $this->total_query_time,
                'replace_time' => $this->total_replace_time,
                'update_time' => $this->total_update_time,
                'time_used' => ($this->time_end - $this->time_start),
            );
        }
    }
}

/************************** URL Replace Functions End ***************************/

/**
 * 显示帮助信息
 */
function showHelp() {
    echo "WordPress URL 替换工具 - PHP版本\n\n";
    echo "用法:\n";
    echo "  php wp_url_replacer.php <站点路径> [选项]\n\n";
    echo "选项:\n";
    echo "  --old-url=URL       旧URL\n";
    echo "  --new-url=URL       新URL\n";
    echo "  --files-only        只替换文件，不处理数据库\n";
    echo "  --db-only           只替换数据库，不处理文件\n";
    echo "  --extensions=EXT    要处理的文件扩展名，用逗号分隔 (默认: css,js,html,htm,php,json,xml)\n";
    echo "  --help              显示此帮助信息\n\n";
    echo "示例:\n";
    echo "  php wp_url_replacer.php /var/www/dev_sites/vincetest\n";
    echo "  php wp_url_replacer.php /var/www/dev_sites/vincetest --old-url=\"http://localhost/vincetest\" --new-url=\"https://vincetest.com\"\n";
    echo "  php wp_url_replacer.php /var/www/dev_sites/vincetest --old-url=\"http://localhost/vincetest\" --new-url=\"https://vincetest.com\" --files-only\n";
}

/**
 * 解析命令行参数
 */
function parseArgs($argv) {
    $args = array(
        'site_path' => '',
        'old_url' => '',
        'new_url' => '',
        'files_only' => false,
        'db_only' => false,
        'extensions' => array('.css', '.js', '.html', '.htm', '.php', '.json', '.xml'),
        'help' => false
    );

    // 第一个参数是站点路径（如果不是选项的话）
    if (isset($argv[1]) && strpos($argv[1], '--') !== 0) {
        $args['site_path'] = $argv[1];
    }

    // 解析其他参数
    for ($i = 2; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if ($arg == '--help') {
            $args['help'] = true;
        } elseif ($arg == '--files-only') {
            $args['files_only'] = true;
        } elseif ($arg == '--db-only') {
            $args['db_only'] = true;
        } elseif (strpos($arg, '--old-url=') === 0) {
            $args['old_url'] = rtrim(substr($arg, 10), '/');
        } elseif (strpos($arg, '--new-url=') === 0) {
            $args['new_url'] = rtrim(substr($arg, 10), '/');
        } elseif (strpos($arg, '--extensions=') === 0) {
            $ext_string = substr($arg, 13);
            $extensions = explode(',', $ext_string);
            $args['extensions'] = array();
            foreach ($extensions as $ext) {
                $args['extensions'][] = '.' . trim($ext);
            }
        }
    }

    return $args;
}

/**
 * 主函数
 */
function main() {
    global $argv;

    $args = parseArgs($argv);

    // 显示帮助
    if ($args['help'] || empty($args['site_path'])) {
        showHelp();
        exit(0);
    }

    // 检查站点路径
    if (!is_dir($args['site_path'])) {
        echo "错误: 站点路径不存在: " . $args['site_path'] . "\n";
        exit(1);
    }

    if (!file_exists($args['site_path'] . '/wp-config.php')) {
        echo "错误: 不是有效的WordPress站点 (缺少wp-config.php): " . $args['site_path'] . "\n";
        exit(1);
    }

    // 创建替换器实例
    $replacer = new WordPressURLReplacer($args['site_path']);

    // 命令行模式
    if (!empty($args['old_url']) && !empty($args['new_url'])) {
        if ($args['old_url'] == $args['new_url']) {
            echo "错误: 新旧URL相同\n";
            exit(1);
        }

        // 解析配置
        if (!$replacer->parseWpConfig()) {
            echo "错误: 无法解析wp-config.php配置\n";
            exit(1);
        }

        echo "开始URL替换: " . $args['old_url'] . " -> " . $args['new_url'] . "\n";

        // 执行替换
        $replace_db = !$args['files_only'];
        $replace_files = !$args['db_only'];

        $replacer->executeReplace($args['old_url'], $args['new_url'], $replace_db, $replace_files);
    } else {
        // 交互式模式
        $replacer->interactiveMode();
    }
}

// 运行主函数 - 只有在非交互模式下才执行
if (php_sapi_name() === 'cli' && !defined('INTERACTIVE_MODE')) {
    main();
} elseif (php_sapi_name() !== 'cli' && !defined('INTERACTIVE_MODE')) {
    echo "此脚本只能在命令行中运行\n";
    exit(1);
}
