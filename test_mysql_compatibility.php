<?php
/**
 * MySQL兼容性测试脚本
 * 检测和修复可能导致URL替换失败的MySQL问题
 */

echo "=== MySQL兼容性测试 ===\n\n";

// 测试站点路径
$test_site = '/var/www/dev_sites/vincetest';

if (!is_dir($test_site) || !file_exists($test_site . '/wp-config.php')) {
    echo "❌ 测试站点不存在或不是有效的WordPress站点\n";
    echo "测试站点路径: $test_site\n";
    exit(1);
}

// 临时修改脚本以避免执行主程序
$content = file_get_contents('interactive_url_replacer.php');
$test_content = str_replace('$replacer->run();', '// $replacer->run(); // 测试模式', $content);
file_put_contents('temp_mysql_test.php', $test_content);

try {
    include 'temp_mysql_test.php';
    
    // 创建测试实例
    $replacer = new InteractiveURLReplacer();
    
    // 手动设置测试站点
    $reflection = new ReflectionClass($replacer);
    $current_site_property = $reflection->getProperty('current_site');
    $current_site_property->setAccessible(true);
    $current_site_property->setValue($replacer, array(
        'path' => $test_site,
        'name' => basename($test_site)
    ));
    
    echo "🔍 测试1: WordPress配置解析\n";
    if (!$replacer->parseWpConfig()) {
        echo "❌ wp-config.php解析失败\n";
        exit(1);
    }
    
    $db_config = $replacer->getDbConfig();
    echo "✅ 配置解析成功\n";
    echo "  - 数据库: {$db_config['DB_NAME']}\n";
    echo "  - 主机: {$db_config['DB_HOST']}\n\n";
    
    echo "🔍 测试2: 数据库连接和SQL模式检查\n";
    
    $mysqli = new mysqli(
        $db_config['DB_HOST'],
        $db_config['DB_USER'],
        $db_config['DB_PASSWORD'],
        $db_config['DB_NAME']
    );
    
    if ($mysqli->connect_error) {
        echo "❌ 数据库连接失败: " . $mysqli->connect_error . "\n";
        exit(1);
    }
    
    echo "✅ 数据库连接成功\n";
    
    // 检查当前SQL模式
    $result = $mysqli->query("SELECT @@sql_mode as sql_mode");
    $row = $result->fetch_assoc();
    $current_sql_mode = $row['sql_mode'];
    
    echo "当前SQL模式: {$current_sql_mode}\n";
    
    $strict_modes = array('STRICT_TRANS_TABLES', 'NO_ZERO_DATE', 'NO_ZERO_IN_DATE');
    $has_strict_mode = false;
    
    foreach ($strict_modes as $mode) {
        if (strpos($current_sql_mode, $mode) !== false) {
            echo "⚠️  检测到严格模式: {$mode}\n";
            $has_strict_mode = true;
        }
    }
    
    if (!$has_strict_mode) {
        echo "✅ 未检测到可能导致问题的严格模式\n";
    }
    
    echo "\n🔍 测试3: 检查无效日期数据\n";
    
    $table_prefix = $db_config['table_prefix'];
    
    $check_queries = array(
        "posts表post_date_gmt" => "SELECT COUNT(*) as count FROM {$table_prefix}posts WHERE post_date_gmt = '0000-00-00 00:00:00'",
        "posts表post_modified_gmt" => "SELECT COUNT(*) as count FROM {$table_prefix}posts WHERE post_modified_gmt = '0000-00-00 00:00:00'",
        "comments表comment_date_gmt" => "SELECT COUNT(*) as count FROM {$table_prefix}comments WHERE comment_date_gmt = '0000-00-00 00:00:00'"
    );
    
    $total_invalid_dates = 0;
    
    foreach ($check_queries as $desc => $query) {
        $result = $mysqli->query($query);
        if ($result) {
            $row = $result->fetch_assoc();
            $count = $row['count'];
            $total_invalid_dates += $count;
            
            if ($count > 0) {
                echo "⚠️  {$desc}: 发现 {$count} 个无效日期\n";
            } else {
                echo "✅ {$desc}: 无无效日期\n";
            }
        }
    }
    
    echo "\n🔍 测试4: 测试SQL模式修改\n";
    
    // 尝试设置兼容模式
    $result = $mysqli->query("SET SESSION sql_mode = 'ALLOW_INVALID_DATES'");
    if ($result) {
        echo "✅ 成功设置兼容SQL模式\n";
        
        // 验证设置
        $result = $mysqli->query("SELECT @@sql_mode as sql_mode");
        $row = $result->fetch_assoc();
        echo "新SQL模式: {$row['sql_mode']}\n";
    } else {
        echo "❌ 设置SQL模式失败: " . $mysqli->error . "\n";
    }
    
    echo "\n🔍 测试5: 测试DomainNameChanger兼容性\n";
    
    try {
        $test_config = array(
            'change_from' => array('http://test-old.com'),
            'change_to' => array('http://test-new.com'),
            'host' => $db_config['DB_HOST'],
            'user' => $db_config['DB_USER'],
            'pw' => $db_config['DB_PASSWORD'],
            'db' => $db_config['DB_NAME'],
            'charset' => $db_config['DB_CHARSET'],
            'debug' => false,
        );
        
        $domain_changer = new DomainNameChanger($test_config);
        echo "✅ DomainNameChanger实例化成功（包含SQL模式修复）\n";
        
    } catch (Exception $e) {
        echo "❌ DomainNameChanger测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n📊 兼容性评估:\n";
    
    $issues = array();
    $recommendations = array();
    
    if ($has_strict_mode) {
        $issues[] = "MySQL严格模式可能导致问题";
        $recommendations[] = "脚本已自动设置兼容模式";
    }
    
    if ($total_invalid_dates > 0) {
        $issues[] = "发现 {$total_invalid_dates} 个无效日期记录";
        $recommendations[] = "脚本将自动修复无效日期";
    }
    
    if (empty($issues)) {
        echo "🎉 数据库完全兼容，无需特殊处理\n";
    } else {
        echo "⚠️  发现以下兼容性问题:\n";
        foreach ($issues as $issue) {
            echo "  - {$issue}\n";
        }
        
        echo "\n💡 解决方案:\n";
        foreach ($recommendations as $rec) {
            echo "  - {$rec}\n";
        }
        
        echo "\n✅ 新版本脚本已包含所有必要的兼容性修复\n";
    }
    
    $mysqli->close();
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('temp_mysql_test.php')) {
        unlink('temp_mysql_test.php');
    }
}

echo "\n=== 兼容性测试完成 ===\n";
echo "\n💡 使用建议:\n";
echo "1. 如果遇到日期相关错误，脚本会自动修复\n";
echo "2. 建议在操作前创建数据库备份\n";
echo "3. 如果仍有问题，可以手动设置MySQL sql_mode\n";
echo "4. 联系管理员调整MySQL配置以获得最佳兼容性\n";
