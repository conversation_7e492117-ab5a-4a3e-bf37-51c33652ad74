#!/bin/bash
# WordPress 批量URL替换脚本
# 用于批量处理多个WordPress站点的URL替换

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_DIR="/var/www/dev_sites"
SCRIPT_PATH="./wp_url_replacer.py"
LOG_DIR="./batch_logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查Python脚本是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}错误: 找不到 wp_url_replacer.py 脚本${NC}"
    exit 1
fi

# 检查基础目录是否存在
if [ ! -d "$BASE_DIR" ]; then
    echo -e "${RED}错误: 基础目录不存在: $BASE_DIR${NC}"
    exit 1
fi

# 函数：显示帮助信息
show_help() {
    echo "WordPress 批量URL替换脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -l, --list              列出所有可用的WordPress站点"
    echo "  -s, --sites SITES       指定要处理的站点（用逗号分隔）"
    echo "  -o, --old-pattern OLD   旧URL模式（使用 {site} 作为站点名占位符）"
    echo "  -n, --new-pattern NEW   新URL模式（使用 {site} 作为站点名占位符）"
    echo "  -d, --db-only           只替换数据库"
    echo "  -f, --files-only        只替换文件"
    echo "  -e, --extensions EXT    指定文件扩展名（用逗号分隔）"
    echo "  -y, --yes               自动确认所有操作"
    echo ""
    echo "示例:"
    echo "  $0 --list"
    echo "  $0 -s vincetest,demoa -o 'http://localhost/{site}' -n 'https://{site}.com'"
    echo "  $0 -s vincetest -o 'http://vincetest.local' -n 'https://vincetest.com' -y"
}

# 函数：列出所有WordPress站点
list_sites() {
    echo -e "${BLUE}扫描WordPress站点...${NC}"
    echo ""
    
    sites=()
    for dir in "$BASE_DIR"/*; do
        if [ -d "$dir" ] && [ -f "$dir/wp-config.php" ]; then
            site_name=$(basename "$dir")
            sites+=("$site_name")
        fi
    done
    
    if [ ${#sites[@]} -eq 0 ]; then
        echo -e "${YELLOW}未找到WordPress站点${NC}"
        return
    fi
    
    echo -e "${GREEN}找到以下WordPress站点:${NC}"
    for i in "${!sites[@]}"; do
        echo "  $((i+1)). ${sites[i]}"
    done
    echo ""
    echo -e "${BLUE}总计: ${#sites[@]} 个站点${NC}"
}

# 函数：处理单个站点
process_site() {
    local site_name="$1"
    local old_url="$2"
    local new_url="$3"
    local options="$4"
    
    local site_path="$BASE_DIR/$site_name"
    local log_file="$LOG_DIR/${site_name}_$(date +%Y%m%d_%H%M%S).log"
    
    echo -e "${BLUE}处理站点: $site_name${NC}"
    echo "  站点路径: $site_path"
    echo "  旧URL: $old_url"
    echo "  新URL: $new_url"
    echo "  日志文件: $log_file"
    
    # 检查站点是否存在
    if [ ! -d "$site_path" ]; then
        echo -e "${RED}  错误: 站点目录不存在${NC}"
        return 1
    fi
    
    if [ ! -f "$site_path/wp-config.php" ]; then
        echo -e "${RED}  错误: 不是有效的WordPress站点${NC}"
        return 1
    fi
    
    # 执行替换
    echo -e "${YELLOW}  开始URL替换...${NC}"
    
    local cmd="python3 $SCRIPT_PATH '$site_path' --old-url '$old_url' --new-url '$new_url' $options"
    
    if eval "$cmd" > "$log_file" 2>&1; then
        echo -e "${GREEN}  ✓ 替换成功${NC}"
        
        # 显示简要统计
        if [ -f "$log_file" ]; then
            local db_stats=$(grep "数据库URL替换完成" "$log_file" | tail -1)
            local file_stats=$(grep "文件URL替换完成" "$log_file" | tail -1)
            
            if [ -n "$db_stats" ]; then
                echo "    $db_stats"
            fi
            
            if [ -n "$file_stats" ]; then
                echo "    $file_stats"
            fi
        fi
        
        return 0
    else
        echo -e "${RED}  ✗ 替换失败${NC}"
        echo -e "${YELLOW}  请查看日志文件: $log_file${NC}"
        return 1
    fi
}

# 函数：确认操作
confirm_operation() {
    local sites_str="$1"
    local old_pattern="$2"
    local new_pattern="$3"
    local options="$4"
    
    echo -e "${YELLOW}即将执行以下操作:${NC}"
    echo "  站点: $sites_str"
    echo "  旧URL模式: $old_pattern"
    echo "  新URL模式: $new_pattern"
    echo "  选项: $options"
    echo ""
    
    if [ "$AUTO_CONFIRM" != "true" ]; then
        read -p "确定要继续吗？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}操作已取消${NC}"
            exit 0
        fi
    fi
}

# 解析命令行参数
SITES=""
OLD_PATTERN=""
NEW_PATTERN=""
OPTIONS=""
AUTO_CONFIRM=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            list_sites
            exit 0
            ;;
        -s|--sites)
            SITES="$2"
            shift 2
            ;;
        -o|--old-pattern)
            OLD_PATTERN="$2"
            shift 2
            ;;
        -n|--new-pattern)
            NEW_PATTERN="$2"
            shift 2
            ;;
        -d|--db-only)
            OPTIONS="$OPTIONS --db-only"
            shift
            ;;
        -f|--files-only)
            OPTIONS="$OPTIONS --files-only"
            shift
            ;;
        -e|--extensions)
            OPTIONS="$OPTIONS --extensions $2"
            shift 2
            ;;
        -y|--yes)
            AUTO_CONFIRM=true
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$SITES" ] || [ -z "$OLD_PATTERN" ] || [ -z "$NEW_PATTERN" ]; then
    echo -e "${RED}错误: 缺少必需参数${NC}"
    echo ""
    show_help
    exit 1
fi

# 解析站点列表
IFS=',' read -ra SITE_ARRAY <<< "$SITES"

# 确认操作
confirm_operation "$SITES" "$OLD_PATTERN" "$NEW_PATTERN" "$OPTIONS"

# 开始批量处理
echo -e "${BLUE}开始批量处理...${NC}"
echo ""

success_count=0
fail_count=0
start_time=$(date +%s)

for site in "${SITE_ARRAY[@]}"; do
    site=$(echo "$site" | xargs) # 去除空格
    
    # 替换占位符
    old_url="${OLD_PATTERN//\{site\}/$site}"
    new_url="${NEW_PATTERN//\{site\}/$site}"
    
    if process_site "$site" "$old_url" "$new_url" "$OPTIONS"; then
        ((success_count++))
    else
        ((fail_count++))
    fi
    
    echo ""
done

# 显示总结
end_time=$(date +%s)
duration=$((end_time - start_time))

echo -e "${BLUE}=== 批量处理完成 ===${NC}"
echo "  成功: $success_count 个站点"
echo "  失败: $fail_count 个站点"
echo "  总用时: ${duration} 秒"
echo "  日志目录: $LOG_DIR"

if [ $fail_count -gt 0 ]; then
    echo -e "${YELLOW}  请检查失败站点的日志文件${NC}"
    exit 1
else
    echo -e "${GREEN}  所有站点处理成功！${NC}"
    exit 0
fi
