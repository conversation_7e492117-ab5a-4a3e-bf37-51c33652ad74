{"批量替换配置示例": {"说明": "这是一个配置文件示例，展示了常见的URL替换场景", "使用方法": "复制此文件为 config.json 并根据需要修改"}, "场景1_本地开发到生产环境": {"描述": "从本地开发环境迁移到生产环境", "站点列表": ["vincetest", "demoa", "boyu"], "旧URL模式": "http://localhost/{site}", "新URL模式": "https://{site}.com", "选项": "--files-only", "命令示例": "./batch_replace.sh -s 'vincetest,demoa,boyu' -o 'http://localhost/{site}' -n 'https://{site}.com'"}, "场景2_HTTP升级HTTPS": {"描述": "将所有站点从HTTP升级到HTTPS", "站点列表": ["vincetest", "she<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "旧URL模式": "http://{site}.com", "新URL模式": "https://{site}.com", "选项": "", "命令示例": "./batch_replace.sh -s 'vincetest,shenghe,weijiewu' -o 'http://{site}.com' -n 'https://{site}.com'"}, "场景3_域名更换": {"描述": "批量更换域名", "站点列表": ["vincetest"], "旧URL模式": "https://old-domain.com", "新URL模式": "https://new-domain.com", "选项": "", "命令示例": "./batch_replace.sh -s 'vincetest' -o 'https://old-domain.com' -n 'https://new-domain.com'"}, "场景4_测试环境配置": {"描述": "本地测试环境URL配置", "站点列表": ["vincetest", "demoa"], "旧URL模式": "http://{site}.local", "新URL模式": "http://localhost/{site}", "选项": "--db-only", "命令示例": "./batch_replace.sh -s 'vincetest,demoa' -o 'http://{site}.local' -n 'http://localhost/{site}' --db-only"}, "常用命令": {"列出所有站点": "./batch_replace.sh --list", "单站点处理": "python wp_url_replacer.py /var/www/dev_sites/vincetest", "只处理CSS和JS": "./batch_replace.sh -s 'vincetest' -o 'http://localhost/vincetest' -n 'https://vincetest.com' -e 'css,js'", "自动确认": "./batch_replace.sh -s 'vincetest' -o 'http://localhost/vincetest' -n 'https://vincetest.com' -y"}, "文件扩展名说明": {"默认处理": ["css", "js", "html", "htm", "php", "json", "xml"], "常用组合": {"前端文件": "css,js,html,htm", "PHP文件": "php", "配置文件": "json,xml", "所有文本": "css,js,html,htm,php,json,xml,txt,md"}}, "注意事项": {"备份": "使用前请务必备份数据库和文件", "测试": "建议先在测试环境中验证", "权限": "确保脚本有读写数据库和文件的权限", "URL格式": "URL不要包含尾部斜杠", "占位符": "使用 {site} 作为站点名占位符"}, "故障排除": {"数据库连接失败": "检查wp-config.php配置和数据库服务状态", "文件权限错误": "使用 chmod +x batch_replace.sh 给脚本执行权限", "Python依赖": "运行 pip install mysql-connector-python 安装依赖", "编码问题": "确保所有文件使用UTF-8编码", "日志查看": "处理失败时查看 batch_logs/ 目录下的日志文件"}}