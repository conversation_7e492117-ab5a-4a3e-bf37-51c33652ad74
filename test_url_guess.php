<?php
/**
 * URL猜测功能测试脚本
 * 用于验证URL猜测功能是否正确返回来源和优先级信息
 */

// 测试独立的interactive_url_replacer.php脚本
// 不再依赖wp_url_replacer.php

echo "=== URL猜测功能测试 ===\n\n";

// 测试站点路径
$test_site = '/var/www/dev_sites/vincetest';

if (!is_dir($test_site) || !file_exists($test_site . '/wp-config.php')) {
    echo "错误: 测试站点不存在或不是有效的WordPress站点\n";
    echo "测试站点路径: $test_site\n";
    exit(1);
}

try {
    // 创建交互式URL替换器实例
    $replacer = new InteractiveURLReplacer();

    // 设置测试站点
    $replacer->current_site = array('path' => $test_site, 'name' => basename($test_site));

    // 解析配置
    if (!$replacer->parseWpConfig()) {
        echo "错误: 无法解析wp-config.php配置\n";
        exit(1);
    }

    echo "✅ 成功解析WordPress配置\n";
    echo "正在执行URL猜测...\n\n";

    // 执行URL猜测
    $old_urls = $replacer->guessOldUrls();
    
    if (empty($old_urls)) {
        echo "⚠️ 未猜测到任何URL\n";
        exit(0);
    }
    
    echo "🎯 猜测结果分析:\n";
    echo "总计发现 " . count($old_urls) . " 个可能的URL\n\n";
    
    // 分析结果
    $source_counts = array();
    $priority_range = array('min' => 10, 'max' => 0);
    
    foreach ($old_urls as $i => $url_info) {
        $url = is_array($url_info) ? $url_info['url'] : $url_info;
        $source = is_array($url_info) ? $url_info['source'] : 'unknown';
        $priority = is_array($url_info) ? $url_info['priority'] : 5;
        
        // 统计来源
        if (!isset($source_counts[$source])) {
            $source_counts[$source] = 0;
        }
        $source_counts[$source]++;
        
        // 统计优先级范围
        if ($priority < $priority_range['min']) {
            $priority_range['min'] = $priority;
        }
        if ($priority > $priority_range['max']) {
            $priority_range['max'] = $priority;
        }
        
        // 显示详细信息
        printf("  %d. %s\n", $i + 1, $url);
        printf("     来源: %s | 优先级: %d\n", $source, $priority);
        printf("     数据类型: %s\n", is_array($url_info) ? 'array' : 'string');
        echo "\n";
    }
    
    // 显示统计信息
    echo "📊 统计信息:\n";
    echo "来源分布:\n";
    foreach ($source_counts as $source => $count) {
        printf("  - %s: %d 个\n", $source, $count);
    }
    
    echo "\n优先级范围: {$priority_range['min']} - {$priority_range['max']}\n";
    
    // 验证修复是否成功
    $has_unknown_sources = isset($source_counts['unknown']);
    $has_valid_priorities = $priority_range['max'] > 5;
    
    echo "\n🔍 修复验证:\n";
    if ($has_unknown_sources) {
        echo "❌ 仍有 'unknown' 来源的URL\n";
    } else {
        echo "✅ 所有URL都有明确的来源信息\n";
    }
    
    if ($has_valid_priorities) {
        echo "✅ 优先级系统正常工作\n";
    } else {
        echo "❌ 优先级系统可能有问题（所有优先级都 <= 5）\n";
    }
    
    // 检查数据结构
    $all_arrays = true;
    foreach ($old_urls as $url_info) {
        if (!is_array($url_info)) {
            $all_arrays = false;
            break;
        }
    }
    
    if ($all_arrays) {
        echo "✅ 所有URL都返回了完整的数组结构\n";
    } else {
        echo "❌ 部分URL仍然是字符串格式\n";
    }
    
    echo "\n";
    if (!$has_unknown_sources && $has_valid_priorities && $all_arrays) {
        echo "🎉 URL猜测功能修复成功！\n";
    } else {
        echo "⚠️ URL猜测功能可能仍有问题，需要进一步检查\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== 测试完成 ===\n";
