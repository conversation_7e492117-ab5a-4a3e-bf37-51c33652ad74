<?php
/**
 * 测试独立性脚本
 * 验证interactive_url_replacer.php是否完全独立，不依赖wp_url_replacer.php
 */

echo "=== 独立性测试 ===\n\n";

// 1. 检查文件是否存在
$files_to_check = array(
    'interactive_url_replacer.php' => '统一交互式脚本',
    'wp_url_replacer.php' => '原始URL替换脚本'
);

foreach ($files_to_check as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc}: {$file} 存在\n";
    } else {
        echo "❌ {$desc}: {$file} 不存在\n";
    }
}

echo "\n";

// 2. 检查interactive_url_replacer.php是否包含必要的类和函数
echo "🔍 检查interactive_url_replacer.php内容:\n";

$content = file_get_contents('interactive_url_replacer.php');

$required_elements = array(
    'class InteractiveURLReplacer' => '主交互类',
    'class DomainNameChanger' => '域名更换器类',
    'function digui_replace' => '递归替换函数',
    'function maybe_serialize' => '序列化函数',
    'function is_serialized' => '序列化检测函数',
    'function parseWpConfig' => 'WordPress配置解析',
    'function replaceDatabaseUrls' => '数据库URL替换',
    'function replaceFileUrls' => '文件URL替换',
    'function guessOldUrls' => 'URL猜测功能'
);

foreach ($required_elements as $element => $desc) {
    if (strpos($content, $element) !== false) {
        echo "✅ {$desc}: 已包含\n";
    } else {
        echo "❌ {$desc}: 缺失\n";
    }
}

echo "\n";

// 3. 检查是否还有对wp_url_replacer.php的依赖
echo "🔍 检查依赖关系:\n";

$dependencies = array(
    'require_once.*wp_url_replacer.php' => 'require_once引入',
    'include.*wp_url_replacer.php' => 'include引入',
    'new WordPressURLReplacer' => '实例化WordPressURLReplacer类',
    '\$this->replacer->' => '调用外部replacer对象'
);

$has_dependencies = false;
foreach ($dependencies as $pattern => $desc) {
    if (preg_match("/{$pattern}/", $content)) {
        echo "⚠️  发现依赖: {$desc}\n";
        $has_dependencies = true;
    }
}

if (!$has_dependencies) {
    echo "✅ 无外部依赖，完全独立\n";
}

echo "\n";

// 4. 语法检查
echo "🔍 语法检查:\n";
$syntax_check = shell_exec('php -l interactive_url_replacer.php 2>&1');
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "✅ 语法检查通过\n";
} else {
    echo "❌ 语法错误:\n{$syntax_check}\n";
}

echo "\n";

// 5. 尝试加载类（不执行主程序）
echo "🔍 类加载测试:\n";

try {
    // 临时重命名main函数调用，避免执行
    $temp_content = str_replace('$replacer->run();', '// $replacer->run();', $content);
    file_put_contents('temp_test.php', $temp_content);
    
    include 'temp_test.php';
    
    if (class_exists('InteractiveURLReplacer')) {
        echo "✅ InteractiveURLReplacer类加载成功\n";
    } else {
        echo "❌ InteractiveURLReplacer类加载失败\n";
    }
    
    if (class_exists('DomainNameChanger')) {
        echo "✅ DomainNameChanger类加载成功\n";
    } else {
        echo "❌ DomainNameChanger类加载失败\n";
    }
    
    if (function_exists('digui_replace')) {
        echo "✅ 序列化处理函数加载成功\n";
    } else {
        echo "❌ 序列化处理函数加载失败\n";
    }
    
    // 清理临时文件
    unlink('temp_test.php');
    
} catch (Exception $e) {
    echo "❌ 类加载测试失败: " . $e->getMessage() . "\n";
    if (file_exists('temp_test.php')) {
        unlink('temp_test.php');
    }
}

echo "\n";

// 6. 统计代码行数
echo "📊 代码统计:\n";
$lines = explode("\n", $content);
$total_lines = count($lines);
$code_lines = 0;
$comment_lines = 0;
$blank_lines = 0;

foreach ($lines as $line) {
    $trimmed = trim($line);
    if (empty($trimmed)) {
        $blank_lines++;
    } elseif (strpos($trimmed, '//') === 0 || strpos($trimmed, '/*') === 0 || strpos($trimmed, '*') === 0) {
        $comment_lines++;
    } else {
        $code_lines++;
    }
}

echo "总行数: {$total_lines}\n";
echo "代码行数: {$code_lines}\n";
echo "注释行数: {$comment_lines}\n";
echo "空白行数: {$blank_lines}\n";

echo "\n";

// 7. 最终评估
echo "🎯 独立性评估:\n";

$score = 0;
$max_score = 5;

// 检查必要类是否存在
if (strpos($content, 'class InteractiveURLReplacer') !== false && 
    strpos($content, 'class DomainNameChanger') !== false) {
    $score++;
    echo "✅ 核心类完整 (+1)\n";
}

// 检查序列化函数是否存在
if (strpos($content, 'function digui_replace') !== false && 
    strpos($content, 'function maybe_serialize') !== false) {
    $score++;
    echo "✅ 序列化处理完整 (+1)\n";
}

// 检查URL替换功能是否存在
if (strpos($content, 'function replaceDatabaseUrls') !== false && 
    strpos($content, 'function replaceFileUrls') !== false) {
    $score++;
    echo "✅ URL替换功能完整 (+1)\n";
}

// 检查是否无外部依赖
if (!$has_dependencies) {
    $score++;
    echo "✅ 无外部依赖 (+1)\n";
}

// 检查语法是否正确
if (strpos($syntax_check, 'No syntax errors') !== false) {
    $score++;
    echo "✅ 语法正确 (+1)\n";
}

echo "\n";
echo "🏆 独立性得分: {$score}/{$max_score}\n";

if ($score == $max_score) {
    echo "🎉 完美！interactive_url_replacer.php 是一个完全独立的统一脚本！\n";
} elseif ($score >= 4) {
    echo "👍 很好！interactive_url_replacer.php 基本独立，可能需要小幅调整\n";
} elseif ($score >= 3) {
    echo "⚠️  一般，interactive_url_replacer.php 需要进一步改进\n";
} else {
    echo "❌ 不合格，interactive_url_replacer.php 仍有重大依赖问题\n";
}

echo "\n=== 测试完成 ===\n";
