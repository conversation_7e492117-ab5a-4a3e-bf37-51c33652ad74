<?php
/**
 * 实时URL获取演示
 * 展示实际的获取过程和结果
 */

// 防止被wp_url_replacer.php的main函数影响
if (php_sapi_name() === 'cli' && basename($_SERVER['PHP_SELF']) === 'live_url_demo.php') {
    // 这是直接运行演示脚本
} else {
    exit("请直接运行此演示脚本\n");
}

// 手动包含必要的类和函数，避免main函数执行
$wp_replacer_content = file_get_contents('wp_url_replacer.php');
// 移除main函数调用
$wp_replacer_content = preg_replace('/if \(php_sapi_name\(\) === \'cli\'\) \{.*?\}/s', '', $wp_replacer_content);
eval('?>' . $wp_replacer_content);

echo "=== 实时URL获取演示 ===\n\n";

$site_path = '/var/www/dev_sites/vincetest';
echo "📍 测试站点: {$site_path}\n\n";

// 创建替换器实例
$replacer = new WordPressURLReplacer($site_path);

// 解析配置
echo "🔧 步骤1: 解析wp-config.php配置\n";
if ($replacer->parseWpConfig()) {
    echo "   ✅ 成功解析数据库配置\n";
    echo "   📊 数据库信息:\n";
    
    // 通过反射获取私有属性（仅用于演示）
    $reflection = new ReflectionClass($replacer);
    $db_config_property = $reflection->getProperty('db_config');
    $db_config_property->setAccessible(true);
    $db_config = $db_config_property->getValue($replacer);
    
    echo "      - 主机: {$db_config['DB_HOST']}\n";
    echo "      - 数据库: {$db_config['DB_NAME']}\n";
    echo "      - 用户: {$db_config['DB_USER']}\n";
    echo "      - 表前缀: {$db_config['table_prefix']}\n";
} else {
    echo "   ❌ 配置解析失败\n";
    exit(1);
}

echo "\n🔍 步骤2: 开始URL猜测过程\n\n";

// 手动演示每个获取步骤
echo "📊 2.1 从数据库获取URL:\n";
try {
    $mysqli = new mysqli(
        $db_config['DB_HOST'],
        $db_config['DB_USER'], 
        $db_config['DB_PASSWORD'],
        $db_config['DB_NAME']
    );
    
    if (!$mysqli->connect_error) {
        $mysqli->set_charset($db_config['DB_CHARSET']);
        $table_prefix = $db_config['table_prefix'];
        
        $query = "SELECT option_name, option_value FROM {$table_prefix}options WHERE option_name IN ('siteurl', 'home')";
        echo "   🔍 执行SQL: {$query}\n";
        
        $result = $mysqli->query($query);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                if (!empty($row['option_value']) && strpos($row['option_value'], 'http') === 0) {
                    echo "   ✅ 找到 {$row['option_name']}: {$row['option_value']}\n";
                }
            }
        }
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: {$e->getMessage()}\n";
}

echo "\n🌐 2.2 获取服务器IP:\n";
$ip_services = array('https://ip.me/ip', 'https://ipinfo.io/ip');
foreach ($ip_services as $service) {
    echo "   🔍 尝试服务: {$service}\n";
    try {
        $context = stream_context_create(array(
            'http' => array(
                'timeout' => 3,
                'user_agent' => 'WordPress URL Replacer Demo'
            )
        ));
        
        $server_ip = @file_get_contents($service, false, $context);
        if ($server_ip && filter_var(trim($server_ip), FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            $server_ip = trim($server_ip);
            echo "   ✅ 获取到IP: {$server_ip}\n";
            echo "   📝 生成URL: http://{$server_ip} 和 https://{$server_ip}\n";
            break;
        } else {
            echo "   ❌ 无效响应或IP格式错误\n";
        }
    } catch (Exception $e) {
        echo "   ❌ 请求失败: {$e->getMessage()}\n";
    }
}

echo "\n🏠 2.3 生成本地开发环境URL:\n";
$site_name = basename($site_path);
echo "   📁 站点目录名: {$site_name}\n";
echo "   📝 生成的本地开发URL:\n";

$dev_patterns = array(
    array('url' => "http://localhost/{$site_name}", 'desc' => '最常见本地开发模式'),
    array('url' => "https://localhost/{$site_name}", 'desc' => 'HTTPS本地开发模式'),
    array('url' => "http://{$site_name}.local", 'desc' => '本地域名模式'),
    array('url' => "https://{$site_name}.local", 'desc' => 'HTTPS本地域名'),
    array('url' => "http://{$site_name}.test", 'desc' => '测试域名模式'),
    array('url' => "http://localhost:8080/{$site_name}", 'desc' => '8080端口模式'),
    array('url' => "http://localhost:8000/{$site_name}", 'desc' => '8000端口模式'),
);

foreach ($dev_patterns as $pattern) {
    echo "      - {$pattern['url']} ({$pattern['desc']})\n";
}

echo "\n🎯 步骤3: 优先级排序和过滤\n";
echo "   📊 优先级规则:\n";
echo "      - 数据库URL: 优先级 10 (最高)\n";
echo "      - 本地localhost: 优先级 9\n";
echo "      - 服务器IP HTTP: 优先级 8\n";
echo "      - 本地HTTPS: 优先级 8\n";
echo "      - 服务器IP HTTPS: 优先级 7\n";
echo "      - 本地域名: 优先级 7-6\n";
echo "      - 常见端口: 优先级 5\n";

echo "\n   🔍 相关性过滤:\n";
echo "      ✅ 保留: 包含站点名、本地环境、服务器IP\n";
echo "      ❌ 过滤: API服务、文档网站、社交媒体\n";

echo "\n📋 最终结果 (按优先级排序):\n";

// 调用实际的猜测方法
$guessOldUrlsMethod = $reflection->getMethod('guessOldUrls');
$guessOldUrlsMethod->setAccessible(true);
$urls = $guessOldUrlsMethod->invoke($replacer);

foreach ($urls as $index => $url) {
    $num = $index + 1;
    echo "   {$num}. {$url}\n";
}

echo "\n🎉 总结:\n";
echo "   - 总共获取到 " . count($urls) . " 个相关URL\n";
echo "   - 第1个URL是数据库中的真实URL (最重要)\n";
echo "   - 其他URL按使用场景的可能性排序\n";
echo "   - 所有URL都经过相关性验证\n";
echo "   - 用户可以快速选择合适的旧URL\n\n";

echo "💡 这就是智能URL猜测的完整过程！\n";
echo "   每个URL都有明确的来源和用途，\n";
echo "   帮助用户快速找到需要替换的旧URL。\n";
